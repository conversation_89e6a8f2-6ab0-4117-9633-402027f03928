import { useQuery } from '@tanstack/react-query';
import { PoolOrder } from '@repo/types/website-api-types';
import type {
  StakeBalanceHistoryQueryParams,
  ColdkeyAlphaSharesQueryParams,
  HotkeyAlphaSharesQueryParams,
  HotkeyEmissionQueryParams,
  PoolHistoryQueryParams,
  PoolQueryParams,
  StakeBalanceAggregatedQueryParams,
  StakeBalanceQueryParams,
  SubnetEmissionQueryParams,
  SubnetPriceQueryParams,
  ValidatorYieldLatestQueryParams,
} from '@repo/types/website-api-types';
import { apiClient, handleResponse } from '@/lib/api/api-client';
import { queryKeys } from '@/lib/constants/query-keys';

const dtaoApiClient = apiClient.dtao;

export const usePool = (params: PoolQueryParams = {}) => {
  return useQuery({
    queryKey: ['pool', params],
    queryFn: async () =>
      handleResponse(await dtaoApiClient.pool.$get({ query: { ...params } })),
  });
};

export const useDtaoSubnetEmission = (
  params: SubnetEmissionQueryParams = {}
) => {
  return useQuery({
    queryKey: ['dtaoSubnetEmission', params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.dtaoSubnetEmission.$get({ query: { ...params } })
      ),
  });
};

export const useHotkeyEmission = (params: HotkeyEmissionQueryParams = {}) => {
  return useQuery({
    queryKey: ['hotkeyEmission', params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.hotkeyEmission.$get({ query: { ...params } })
      ),
  });
};

export const useHotkeyAlphaShares = (
  params: HotkeyAlphaSharesQueryParams = {}
) => {
  return useQuery({
    queryKey: ['hotkeyAlphaShares', params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.hotkeyAlphaShares.$get({ query: { ...params } })
      ),
  });
};

export const useColdkeyAlphaShares = (
  params: ColdkeyAlphaSharesQueryParams = {}
) => {
  return useQuery({
    queryKey: ['coldkeyAlphaShares', params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.coldkeyAlphaShares.$get({ query: { ...params } })
      ),
  });
};

export const useStakeBalance = (params: StakeBalanceQueryParams = {}) => {
  return useQuery({
    queryKey: ['stakeBalance', params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.stakeBalance.$get({ query: { ...params } })
      ),
  });
};

export const useStakeBalanceAggregated = (
  params: StakeBalanceAggregatedQueryParams = {}
) => {
  return useQuery({
    queryKey: ['stakeBalanceAggregated', params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.stakeBalanceAggregated.$get({
          query: { ...params },
        })
      ),
  });
};

export const useDtaoSubnets = (
  params: PoolQueryParams = { order: PoolOrder.NetuidAsc }
) => {
  return useQuery({
    queryKey: [queryKeys.dtaoSubnetPools, params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.dtaoSubnets.$get({
          query: { ...params },
        })
      ),
  });
};

export const useHeatmapHotkeyNetuid = () => {
  return useQuery({
    queryKey: ['heatmapHotkeyNetuid'],
    queryFn: async () =>
      handleResponse(await dtaoApiClient.heatmapHotkeyNetuid.$get()),
  });
};

export const useValidatorYieldLatest = (
  params: ValidatorYieldLatestQueryParams = {}
) => {
  return useQuery({
    queryKey: ['validatorYieldLatest', params],
    queryFn: async () =>
      handleResponse(
        await dtaoApiClient.validatorYieldLatest.$get({
          query: { ...params },
        })
      ),
  });
};

export const useSubnetPrice = (
  params: SubnetPriceQueryParams = { id: -1, type: '', timestamp_start: '' }
) => {
  return useQuery({
    queryKey: ['subnetPrice', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const {
        id,
        timestamp_start: timestampStart,
        type,
      } = queryParams as SubnetPriceQueryParams;

      if (id === -1 && timestampStart === '' && type === '') return [];

      return handleResponse(
        await dtaoApiClient.subnetPrice.$get({
          query: {
            id,
            type,
            timestampStart,
          },
        })
      );
    },
  });
};

export const useTaoStakedAlpha = (params: PoolHistoryQueryParams = {}) => {
  return useQuery({
    queryKey: ['taoStakedAlpha', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { frequency, timestamp_start: timestampStart } =
        queryParams as PoolHistoryQueryParams;

      return handleResponse(
        await dtaoApiClient.taoStakedAlpha.$get({
          query: {
            frequency,
            timestamp_start: timestampStart,
          },
        })
      );
    },
  });
};

export const useTaoStakedRoot = (params: PoolHistoryQueryParams = {}) => {
  return useQuery({
    queryKey: ['taoStakedRoot', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { frequency, timestamp_start: timestampStart } =
        queryParams as PoolHistoryQueryParams;

      return handleResponse(
        await dtaoApiClient.taoStakedRoot.$get({
          query: {
            frequency,
            timestamp_start: timestampStart,
          },
        })
      );
    },
  });
};

export const useSubnetTotalPrice = () => {
  return useQuery({
    queryKey: ['subnetTotalPrice'],
    queryFn: async () =>
      handleResponse(await dtaoApiClient.subnetTotalPrice.$get()),
  });
};

export const useStakeBalanceHistory = (
  params: StakeBalanceHistoryQueryParams = {
    coldkey: '',
    hotkey: '',
    netuid: -1,
  }
) => {
  return useQuery({
    queryKey: ['stakeBalanceHistory', params],
    queryFn: async ({ queryKey: [, queryParams] }) => {
      const { coldkey, hotkey, netuid } =
        queryParams as StakeBalanceHistoryQueryParams;

      if (coldkey === '' || hotkey === '' || netuid === -1) {
        return [];
      }

      return handleResponse(
        await dtaoApiClient.stakeBalanceHistory.$get({
          query: {
            coldkey,
            hotkey,
            netuid,
          },
        })
      );
    },
  });
};
