import { useEffect } from 'react';
import { useLatestBlock } from './block-hooks';
import { useDtaoSubnets } from './dtao-hooks';
import { usePrice } from './price-hooks';
import { useSubnetIdentityData } from './subnet-hooks';
import { useSubnetNames } from './use-subnet-name';
import { useValidatorMetadataAtom } from './use-validator-metadata';
import { useDtaoValidators, useValidatorIdentityData } from './validator-hooks';
import { useLatestPriceAtom } from '@/store/use-latest-price';

export const useNavigationData = () => {
  const priceQuery = usePrice();
  const blockQuery = useLatestBlock();
  const subnetIdentitiesQuery = useSubnetIdentityData();
  const dtaoValidatorsQuery = useDtaoValidators({});
  const validatorIdentitiesQuery = useValidatorIdentityData();
  const dtaoSubnetPoolsQuery = useDtaoSubnets();
  const { setLatestPrice } = useLatestPriceAtom();
  const { setSubnetMetadata } = useSubnetNames();
  const { setValidatorMetadata } = useValidatorMetadataAtom();

  useEffect(() => {
    setLatestPrice(Number(priceQuery.data?.data[0].price ?? 0));
  }, [priceQuery, setLatestPrice]);

  useEffect(() => {
    setSubnetMetadata(subnetIdentitiesQuery.data?.data ?? []);
  }, [setSubnetMetadata, subnetIdentitiesQuery]);

  useEffect(() => {
    setValidatorMetadata(validatorIdentitiesQuery.data ?? []);
  }, [setValidatorMetadata, validatorIdentitiesQuery]);

  return {
    priceData: priceQuery.data?.data[0],
    latestBlock: blockQuery.data?.data[0]?.block_number ?? 0,
    subnetMetaData: subnetIdentitiesQuery.data?.data ?? [],
    validatorData: dtaoValidatorsQuery.data ?? [],
    validatorMetadata: validatorIdentitiesQuery.data ?? [],
    subnetsData: dtaoSubnetPoolsQuery.data?.data ?? [],
    isLoading:
      priceQuery.isLoading ||
      blockQuery.isLoading ||
      subnetIdentitiesQuery.isLoading ||
      dtaoValidatorsQuery.isLoading ||
      validatorIdentitiesQuery.isLoading ||
      dtaoSubnetPoolsQuery.isLoading,
    isError:
      priceQuery.isError ||
      blockQuery.isError ||
      subnetIdentitiesQuery.isError ||
      dtaoValidatorsQuery.isError ||
      validatorIdentitiesQuery.isError ||
      dtaoSubnetPoolsQuery.isError,
  };
};
