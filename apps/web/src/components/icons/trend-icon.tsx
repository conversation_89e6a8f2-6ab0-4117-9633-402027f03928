import { memo } from 'react';
import { MdCallReceived, MdOutlineNorthEast } from 'react-icons/md';
import { cn } from '@repo/ui/lib';

function TrendIcon({
  isPositive,
  text,
}: {
  isPositive: boolean;
  text?: string;
}) {
  const icon = isPositive ? (
    <MdOutlineNorthEast size={26} />
  ) : (
    <MdCallReceived size={26} />
  );

  return (
    <div className='flex items-end align-bottom text-base'>
      <div
        className={cn(
          'flex flex-row items-center justify-center gap-0.5 rounded-full p-1',
          isPositive
            ? 'bg-[#BAEB471A] text-[#00DBBC]'
            : 'bg-[#EB53471A] text-[#EB5347]',
          text && 'px-2'
        )}
      >
        {icon}
        {text}
      </div>
    </div>
  );
}

export default memo(TrendIcon);
