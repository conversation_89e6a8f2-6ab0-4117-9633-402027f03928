'use client';

import { useAtomValue } from 'jotai';
import { useMemo } from 'react';

export default function ColdkeyHeader({
  id,
  data,
  totalStake,
  dailyReward,
}: {
  id: string;
  data?: MetagraphAPI;
  totalStake: number;
  dailyReward: number;
}) {
  const { windowSize } = useWindowSize();
  const taoValue = useAtomValue(latestPriceAtom);
  const headerInfo = useMemo(() => {
    const coldkeyData = data?.data;

    const hotkeys: string[] = [];

    coldkeyData?.map(({ hotkey, daily_reward, stake }) => {
      if (!hotkeys.includes(hotkey.ss58)) hotkeys.push(hotkey.ss58);
      else return;
    });

    return [
      {
        label: 'Coldkey',
        children: (
          <div className='w-fit'>
            <KeyWrapper
              coldkey={id}
              fullWidth={
                ((windowSize?.width ?? 0) > 730 ||
                  (windowSize?.width ?? 0) < 640) &&
                (windowSize?.width ?? 0) > 564
              }
              className='!text-sm'
              linkToAccount
            />
          </div>
        ),
      },
      {
        label: 'Total Neurons',
        children: <Text level={'md'}>{coldkeyData?.length ?? 0}</Text>,
      },
      {
        label: 'Total Hotkeys',
        children: <Text level={'md'}>{hotkeys.length}</Text>,
      },
      {
        label: 'Daily Rewards',
        children: (
          <div className='flex items-center gap-4'>
            <Text level={'md'} className='flex items-center'>
              <Bittensor />
              {(dailyReward / taoDivider).toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 3,
              })}
            </Text>
            <Text level={'md'}>{`($${(
              (dailyReward / taoDivider) *
              Number(taoValue)
            ).toLocaleString('en-US', {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })})`}</Text>
          </div>
        ),
      },
      {
        label: 'Total Stake',
        children: (
          <div className='flex items-center gap-4'>
            <Text level={'md'} className='flex items-center'>
              <Bittensor />
              {(totalStake / taoDivider).toLocaleString('en-US', {
                minimumFractionDigits: 0,
                maximumFractionDigits: 2,
              })}
            </Text>
            <Text level={'md'}>{`($${(
              (totalStake / taoDivider) *
              Number(taoValue)
            ).toLocaleString('en-US', {
              minimumFractionDigits: 0,
              maximumFractionDigits: 2,
            })})`}</Text>
          </div>
        ),
      },
    ];
  }, [data?.data, id, windowSize, dailyReward, taoValue, totalStake]);

  return (
    <div className='flex flex-col gap-4'>
      <Breadcrumbs />
      <div className='flex flex-col gap-2 px-4 pt-6 sm:px-10'>
        {headerInfo.map(({ label, children }, index) => (
          <div
            className='h-max w-full flex-col items-start gap-2 rounded-xl border border-[#CBCBCB1A] bg-[#161616] px-6 py-2 max-sm:space-y-2 sm:flex sm:h-10 sm:w-max sm:flex-row sm:items-center'
            key={index}
          >
            <Text level={'md'} className='w-32 text-[#777777]'>
              {label}
            </Text>
            {children}
          </div>
        ))}
      </div>
    </div>
  );
}
