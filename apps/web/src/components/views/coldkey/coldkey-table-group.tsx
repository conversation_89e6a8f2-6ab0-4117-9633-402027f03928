"use client";

import type { Metagraph, MetagraphAP<PERSON> } from "@/types";
import { useMemo } from "react";
import { SubSubnetTableDynamic } from "./sub-subnet-table.dynamic";

export default function ColdkeyTableGroup({ data }: { data?: MetagraphAPI }) {
	const metaData = useMemo<Metagraph[][]>(() => {
		return (
			data?.data.reduce((acc: Metagraph[][], item: Metagraph) => {
				const index = acc.findIndex((one) => one[0].netuid === item.netuid);

				if (index >= 0) {
					acc[index].push(item);
				} else {
					acc.push([item]);
				}

				return acc;
			}, []) ?? []
		);
	}, [data]);

	return metaData
		.sort((a, b) => a[0].netuid - b[0].netuid)
		.map((item, index) => (
			<SubSubnetTableDynamic
				data={item}
				subnetID={`${item[0].netuid}`}
				key={index}
			/>
		));
}
