import { Skeleton } from '@repo/ui/components';
import dynamic from 'next/dynamic';

export const ColdkeyHeaderDynamic = dynamic(
  () => import('./coldkey-header').then((mod) => mod.default),
  {
    ssr: false,
    loading: () => (
      <div className='flex flex-col gap-4'>
        <Breadcrumbs />
        <div className='flex flex-col gap-2 px-4 pt-6 sm:px-10'>
          {Array.from({ length: 5 }).map((_, index) => (
            <Skeleton key={index} className='w-70 h-10' />
          ))}
        </div>
      </div>
    ),
  }
);
