'use client';

import { useEffect, useMemo, useState } from 'react';
import type {
  MetagraphAPI,
  MetagraphParamsAtom,
} from '@repo/types/website-api-types';
import { ColdkeyHeaderDynamic } from './coldkey-header.dynamic';
import ColdkeyTableGroup from './coldkey-table-group';
import { useMetagraph } from '@/lib/hooks';

export default function ColdkeyWrapper({
  id,
  initialData,
  totalStake,
  dailyReward,
}: {
  id: string;
  initialData: MetagraphAPI | null;
  totalStake: number;
  dailyReward: number;
}) {
  const [isInit, setIsInit] = useState<boolean>(Boolean(initialData));

  const [validatorPerformanceParams, setValidatorPerformanceParams] =
    useState<MetagraphParamsAtom>({});

  const { data, isPending } = useMetagraph(validatorPerformanceParams);

  const coldkeyData = useMemo(
    () => (isInit && initialData ? initialData : data),
    [data, initialData, isInit]
  );

  useEffect(() => {
    setValidatorPerformanceParams({ coldkey: id });
  }, [id, setValidatorPerformanceParams]);

  useEffect(() => {
    if (!isPending) {
      setIsInit(false);
    }
  }, [isPending]);

  return (
    <div className='flex flex-col gap-8 pb-14 sm:gap-12'>
      <ColdkeyHeaderDynamic
        data={coldkeyData}
        id={id}
        totalStake={totalStake}
        dailyReward={dailyReward}
      />
      <ColdkeyTableGroup data={coldkeyData} />
    </div>
  );
}
