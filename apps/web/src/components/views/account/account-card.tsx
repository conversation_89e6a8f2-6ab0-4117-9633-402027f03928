import type { PropsWithChildren, ReactNode } from 'react';
import { Text } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';

export function AccountCard({
  title,
  icon,
  tag,
  children,
  className,
}: PropsWithChildren<{
  title: string;
  icon: ReactNode;
  tag?: string;
  className?: string;
  isPending: boolean;
}>) {
  return (
    <div
      className={cn(
        'flex min-w-[190px] flex-1 flex-col justify-start gap-2 rounded-xl border border-neutral-800 bg-neutral-800/70 p-2 sm:p-5',
        className
      )}
    >
      <div className='flex flex-row'>
        <Text
          level='md'
          className='flex flex-1 items-center gap-2 text-neutral-400'
        >
          {icon}
          {title}
        </Text>
        {tag ? (
          <Text
            level='md'
            className='flex items-center gap-2 rounded-full border border-[#323232] bg-[#1D1D1D] bg-neutral-700/10 px-3 py-1 text-neutral-400'
          >
            {tag}
          </Text>
        ) : null}
      </div>
      <div className='flex flex-grow flex-col items-start gap-2'>
        {children}
      </div>
    </div>
  );
}
