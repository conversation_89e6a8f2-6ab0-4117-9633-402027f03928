'use client';

import { memo, useMemo } from 'react';
import { LinearGradient } from '@visx/gradient';
import {
  AreaSeries,
  Axis,
  Grid,
  Tooltip,
  XYChart,
  buildChartTheme,
} from '@visx/xychart';
import { utc } from 'moment';
import { format } from 'numerable';
import { Separator, Skeleton, Text } from '@repo/ui/components';
import { cn, taoDivider, useWindowSize } from '@repo/ui/lib';
import { Bittensor } from '@/components/icons/bittensor';
import { DtaoWrapper } from '@/components/views/dtao';
import { useStakeBalanceHistory } from '@/lib/hooks';
import { walletFormat } from '@/lib/utils';

type DataPoint = {
  x: string | number;
  y: number;
  label: string;
  balance: number;
  balanceAsTao: number;
};

type NearestDatum = {
  datum: DataPoint;
  key: string;
};

const MainChart = memo(
  ({
    id,
    hotkey,
    netuid,
    subnetSymbol,
    currency,
  }: {
    id: string;
    hotkey: string;
    netuid: number;
    subnetSymbol: string;
    currency: 'TAO' | 'USD';
  }) => {
    const { data: stakeBalanceHistoryData, isPending } = useStakeBalanceHistory(
      {
        coldkey: id,
        netuid,
        hotkey,
      }
    );

    const totalBalanceLabel = 'Total Balance';

    const hotkeyBalanceData = useMemo(() => {
      return (
        stakeBalanceHistoryData?.data
          .filter((item) => item.hotkey?.ss58 === hotkey)
          .sort((a, b) => b.timestamp.localeCompare(a.timestamp)) ?? []
      );
    }, [hotkey, stakeBalanceHistoryData]);

    const balanceDatas = useMemo(() => {
      const result: DataPoint[] = hotkeyBalanceData.map((item) => ({
        x: item.timestamp,
        y: Number(item.balance) / taoDivider,
        label: item.hotkey_name || item.hotkey?.ss58,
        balance: Number(item.balance) / taoDivider,
        balanceAsTao: Number(item.balance_as_tao) / taoDivider,
      }));

      return result;
    }, [hotkeyBalanceData]);

    const conversionFactor = useMemo(() => {
      const highestAlpha = Math.max(...balanceDatas.map((fb) => fb.balance));
      const highestTao = Math.max(...balanceDatas.map((fb) => fb.balanceAsTao));

      return highestTao > 0 ? highestAlpha / highestTao : 1;
    }, [balanceDatas]);

    const aggregatedTaoBalance = useMemo(() => {
      if (!stakeBalanceHistoryData) {
        return [];
      }

      const allTimeStampHistories = stakeBalanceHistoryData
        .flatMap((s) =>
          s.data.map((d) => {
            return {
              timestamp: d.timestamp,
              hotkeyBalance: {
                hotkey: d.hotkey.ss58,
                balance: Number(d.balance),
                balance_as_tao: Number(d.balance_as_tao),
              },
            };
          })
        )
        .sort((a, b) => (a.timestamp > b.timestamp ? 1 : -1));

      const taoByTimestamp: {
        timestamp: string;
        hotkeyBalances: {
          hotkey: string;
          balance: number;
          balance_as_tao: number;
        }[];
      }[] = [];

      let indexTracker = -1;

      allTimeStampHistories.map(({ timestamp, hotkeyBalance }) => {
        const isFirst = indexTracker < 0;
        const isNewTimestamp =
          isFirst || timestamp !== taoByTimestamp[indexTracker].timestamp;

        if (isNewTimestamp) {
          // We carry forward the hotkey balances as sometimes we may only have a single item update
          const prevHotkeyBalances = isFirst
            ? []
            : taoByTimestamp[indexTracker].hotkeyBalances.map((b) => {
                return { ...b };
              });
          taoByTimestamp.push({
            timestamp,
            hotkeyBalances: prevHotkeyBalances,
          });
          indexTracker++;
        }

        const hotkeyIndex = taoByTimestamp[
          indexTracker
        ].hotkeyBalances.findIndex((b) => b.hotkey === hotkeyBalance.hotkey);
        if (hotkeyIndex < 0) {
          // Hotkey does not exist for this timestamp
          taoByTimestamp[indexTracker].hotkeyBalances.push({
            ...hotkeyBalance,
          });
        } else {
          taoByTimestamp[indexTracker].hotkeyBalances[hotkeyIndex] = {
            ...hotkeyBalance,
          };
        }
      });

      const result: DataPoint[] = taoByTimestamp.map((t) => {
        const balanceAsTao =
          t.hotkeyBalances
            .map((hkb) => hkb.balance_as_tao)
            .reduce((accumulator, currentValue) => accumulator + currentValue) /
          taoDivider;

        const balance =
          t.hotkeyBalances
            .map((hkb) => hkb.balance)
            .reduce((accumulator, currentValue) => accumulator + currentValue) /
          taoDivider;

        return {
          x: t.timestamp,
          y: balanceAsTao * conversionFactor,
          label: totalBalanceLabel,
          balance,
          balanceAsTao,
        };
      });

      return result;
    }, [stakeBalanceHistoryData, conversionFactor]);

    const alphaBalanceScale = useMemo(() => {
      return Math.max(...balanceDatas.map((d) => d.balance));
    }, [balanceDatas]);

    const taoBalanceScale = useMemo(() => {
      return Math.max(...aggregatedTaoBalance.map((b) => b.balanceAsTao));
    }, [aggregatedTaoBalance]);

    const find24hrBalanceHistoryData = useMemo(
      () =>
        hotkeyBalanceData.find(
          (d) => utc(d.timestamp) <= utc().subtract(1, 'day')
        ),
      [hotkeyBalanceData]
    );

    const twentyFourHourChangeAlpha = useMemo(
      () =>
        hotkeyBalanceData
          ? ((Number(hotkeyBalanceData[0]?.balance) ?? 0) -
              (Number(find24hrBalanceHistoryData?.balance) ?? 0)) /
            taoDivider
          : 0,
      [find24hrBalanceHistoryData?.balance, hotkeyBalanceData]
    );

    const twentyFourHourChangeTao = useMemo(
      () =>
        hotkeyBalanceData
          ? ((Number(hotkeyBalanceData[0]?.balance_as_tao) ?? 0) -
              (Number(find24hrBalanceHistoryData?.balance_as_tao) ?? 0)) /
            taoDivider
          : 0,
      [find24hrBalanceHistoryData?.balance_as_tao, hotkeyBalanceData]
    );

    const customTheme = buildChartTheme({
      backgroundColor: '',
      colors: ['#04ad96', '#EB5347'],
      gridColor: '#e8e8e8',
      gridColorDark: '#222831',
      tickLength: 1,
    });

    const isMobile = useWindowSize().isMobile;

    const accessors = {
      xAccessor: (d: DataPoint) => new Date(d.x),
      yAccessor: (d: DataPoint) => d.y,
    };

    if (balanceDatas.length > 0) {
      return !isPending ? (
        <div>
          <div className='flex flex-row gap-2 pb-2'>
            <div className='flex flex-col gap-2 rounded-xl border border-neutral-800 bg-neutral-800/70 p-2 sm:p-5'>
              <Text
                level='md'
                className='flex flex-1 items-center gap-2 text-neutral-400'
              >
                24h Change Alpha
              </Text>
              <DtaoWrapper
                amount={twentyFourHourChangeAlpha}
                maximumFractionDigits={4}
                minimumFractionDigits={0}
                suffix={<span className='mr-1'>{subnetSymbol}</span>}
                variant='Stake'
                number
              />
            </div>
            <div className='flex flex-col gap-2 rounded-xl border border-neutral-800 bg-neutral-800/70 p-2 sm:p-5'>
              <Text
                level='md'
                className='flex flex-1 items-center gap-2 text-neutral-400'
              >
                24h Change {currency}
              </Text>
              <DtaoWrapper
                amount={twentyFourHourChangeTao}
                variant={currency === 'USD' ? 'MarketCap' : 'Stake'}
                currentUSD={currency === 'USD'}
                number
              />
            </div>
          </div>
          <XYChart
            height={400}
            xScale={{ type: 'time' }}
            yScale={{
              type: 'linear',
              zero: true,
            }}
            margin={{
              top: 4,
              right: isMobile ? 60 : 80,
              bottom: 40,
              left: isMobile ? 60 : 80,
            }}
            theme={customTheme}
          >
            <Grid
              rows={false}
              lineStyle={{
                stroke: '#FFFFFF',
                strokeOpacity: 0.2,
                strokeWidth: 1,
                strokeDasharray: '4 4',
              }}
            />
            <Grid
              columns={false}
              numTicks={8}
              lineStyle={{
                stroke: '#FFFFFF',
                strokeOpacity: 0.2,
                strokeWidth: 1,
                strokeDasharray: '0',
              }}
            />
            <defs>
              <LinearGradient
                id='balanceAsTaoGradient'
                from='#00DBBC'
                fromOpacity={0.2}
                to='#00DBBC'
                toOpacity={0}
                x1='100%'
                y1='0%'
                x2='100%'
                y2='100%'
              />
              <LinearGradient
                id='gradient2'
                from='#EB5347'
                fromOpacity={0.2}
                to='#EB5347'
                toOpacity={0}
                x1='100%'
                y1='0%'
                x2='100%'
                y2='100%'
              />
            </defs>
            <Axis
              hideAxisLine
              hideTicks
              orientation='right'
              numTicks={8}
              label='Tao Balance'
              tickFormat={(taoBalance) => {
                const scale = taoBalance / conversionFactor;
                return format(scale, taoBalanceScale < 10 ? '0.00' : '0,0 a');
              }}
              labelOffset={isMobile ? 40 : 50}
              labelProps={{
                fill: '#EB5347',
                fontSize: 12,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
              }}
              tickLabelProps={() => ({
                fill: '#EB5347',
                fontSize: 12,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
                dx: isMobile ? '5px' : '15px',
              })}
            />
            <Axis
              hideAxisLine
              hideTicks
              orientation='left'
              numTicks={8}
              label={`Alpha Balance ${subnetSymbol && `( ${subnetSymbol} )`}`}
              labelOffset={isMobile ? 30 : 40}
              labelProps={{
                fill: '#00dbbc',
                fontSize: 12,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
                dx: '-5px',
              }}
              tickLabelProps={() => ({
                fill: '#00dbbc',
                fontSize: 12,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
                dx: '-5px',
              })}
              tickFormat={(alphaValue: number) =>
                format(alphaValue, alphaBalanceScale < 10 ? '0.00' : '0,0 a')
              }
            />
            <Axis
              orientation='bottom'
              tickLength={4}
              tickStroke='#FFFFFF4D'
              numTicks={isMobile ? 4 : 10}
              tickFormat={(date: string) => {
                return utc(date).format('DD MMM');
              }}
              tickLabelProps={() => ({
                fill: '#FFFFFF',
                fontSize: 12,
                opacity: 0.4,
                fontWeight: 500,
                fontFamily: 'var(--font-sans)',
                dy: '10px',
              })}
            />
            <AreaSeries
              dataKey='Line 2'
              data={balanceDatas.slice().reverse()}
              yAccessor={(d) => d.y}
              xAccessor={accessors.xAccessor}
              fill='url(#balanceAsTaoGradient)'
              lineProps={{
                strokeWidth: 2,
              }}
            />
            <AreaSeries
              dataKey='Total'
              data={aggregatedTaoBalance.slice()}
              yAccessor={(d) => d.y}
              xAccessor={accessors.xAccessor}
              fill='url(#gradient2)'
              lineProps={{
                strokeWidth: 2,
              }}
            />
            <Tooltip
              snapTooltipToDatumX
              snapTooltipToDatumY
              showVerticalCrosshair
              showSeriesGlyphs
              verticalCrosshairStyle={{
                stroke: '#FFFFFF99',
                strokeDasharray: '4 4',
              }}
              renderTooltip={({ tooltipData }) => {
                if (!tooltipData || !tooltipData.nearestDatum) {
                  return null;
                }

                const { datum } = tooltipData.nearestDatum as NearestDatum;

                return (
                  <div className='z-10 flex flex-col gap-2 rounded-lg border border-[#323232] bg-[#1D1D1D]/60 p-3 backdrop-blur-xl'>
                    <Text level='sm' className='font-medium opacity-70'>
                      {utc(datum.x).format('DD MMM YYYY HH:mm UTC')}
                    </Text>
                    <Separator />
                    <div className='gap-0.1 flex flex-col'>
                      <div className='flex flex-col gap-0.5'>
                        <Text
                          className={cn(
                            'flex items-center',
                            datum.label === totalBalanceLabel
                              ? 'text-[#EB5347]'
                              : 'text-[#00dbbc]'
                          )}
                          level='base'
                        >
                          {datum.label === totalBalanceLabel
                            ? datum.label
                            : walletFormat(datum.label)}
                        </Text>
                      </div>
                      <div className='flex flex-col gap-0.5'>
                        <Text level='sm' className='max-w-60 opacity-50'>
                          Balance
                        </Text>
                        <div className='flex flex-row'>
                          <Text
                            className='-mb-0.5 -ml-2 -mr-1 flex items-center px-2 text-white opacity-80'
                            level='xs'
                          >
                            {subnetSymbol}
                          </Text>
                          <Text
                            className='flex items-center text-white opacity-80'
                            level='base'
                          >
                            {datum.balance}
                          </Text>
                        </div>
                      </div>
                      <div className='flex flex-col gap-0.5'>
                        <Text level='sm' className='max-w-60 opacity-50'>
                          Balance as Tao
                        </Text>
                        <Text
                          className='flex items-center text-white opacity-80'
                          level='base'
                        >
                          <Bittensor className='-mb-0.5 -ml-2 -mr-1' />
                          {datum.balanceAsTao}
                        </Text>
                      </div>
                    </div>
                  </div>
                );
              }}
            />
          </XYChart>
        </div>
      ) : (
        <Skeleton className='h-[400px] w-full' />
      );
    }
  }
);

MainChart.displayName = 'MainChart';

export const StakeBalanceHistoryChart = ({
  id,
  hotkey,
  netuid,
  subnetSymbol,
  currency,
}: {
  id: string;
  hotkey: string;
  netuid: number;
  subnetSymbol: string;
  currency: 'TAO' | 'USD';
}) => {
  return (
    <div className='w-full min-w-0 space-y-4 rounded-lg border border-neutral-800 bg-neutral-800/30 px-2.5 py-8'>
      <MainChart
        id={id}
        netuid={netuid}
        subnetSymbol={subnetSymbol}
        hotkey={hotkey}
        currency={currency}
      />
    </div>
  );
};
