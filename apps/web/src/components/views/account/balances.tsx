import type {
  DtaoSubnetsAPI,
  StakeBalance,
} from '@repo/types/website-api-types';
import { DtaoWalletTable } from './wallet-table';

export default function Balances({
  params: { id },
  stakeBalanceData,
  subnets,
}: {
  params: { id: string };
  stakeBalanceData: StakeBalance[] | null;
  subnets: DtaoSubnetsAPI | null;
}) {
  return (
    <div className='flex flex-col gap-10'>
      <DtaoWalletTable
        coldkey={id}
        subnets={subnets}
        stakeBalanceData={stakeBalanceData}
      />
    </div>
  );
}
