'use client';

import { useMemo } from 'react';
import { format } from 'date-fns';
import { Calendar, ChartSpline, Wallet } from 'lucide-react';
import { utc } from 'moment';
import { BiCubeAlt } from 'react-icons/bi';
import type { ColdkeySwap } from '@repo/types/website-api-types';
import { CopyButton, Link, Skeleton, Text } from '@repo/ui/components';
import { appRoutes, taoDivider, useWindowSize } from '@repo/ui/lib';
import { AccountCard } from './account-card';
import { SpinnerDiv } from '@/components/elements/loading/spinner';
import { Bittensor } from '@/components/icons/bittensor';
import TrendIcon from '@/components/icons/trend-icon';
import { PriceFormatter } from '@/components/views/dtao/single-subnet/price-formatter';
import { useAccount } from '@/lib/hooks';
import { walletFormat } from '@/lib/utils';
import { useLatestPriceAtom } from '@/store/use-latest-price';

const getColdkeySwapData = (
  currentId: string,
  coldkeySwap: ColdkeySwap | null
) => {
  if (!coldkeySwap) {
    return null;
  }

  const isNewColdkey = coldkeySwap.new_coldkey.ss58 === currentId;
  return {
    label: `Address ${
      isNewColdkey ? 'before' : 'after'
    } coldkey swap on ${format(coldkeySwap.timestamp, 'do MMMM yyyy')}:`,
    value: isNewColdkey
      ? coldkeySwap.old_coldkey.ss58
      : coldkeySwap.new_coldkey.ss58,
  };
};

export default function WalletHeader({ id }: { id: string }) {
  const { windowSize } = useWindowSize();
  const { latestPrice } = useLatestPriceAtom();
  const { data } = useAccount({ address: id });

  const wallet = useMemo(() => {
    const current = data?.data[0];

    if (!current) return undefined;

    const balanceChange = (currentBalance: string, previousBalance: string) =>
      Number.parseFloat(currentBalance) - Number.parseFloat(previousBalance);

    const balanceChangePercent = (
      currentBalance: string,
      previousBalance: string
    ) =>
      Number(previousBalance)
        ? (balanceChange(currentBalance, previousBalance) /
            Number.parseFloat(previousBalance)) *
          100
        : 0;

    const balancePercentOfTotal = (balance: string) =>
      !Number(current.balance_total)
        ? 0
        : (Number.parseFloat(balance) /
            Number.parseFloat(current.balance_total)) *
          100;

    return {
      id: current.address.ss58,
      publicKey: current.address.hex,
      balance_staked: Number.parseFloat(current.balance_staked) / taoDivider,
      balaced_staked_root:
        Number.parseFloat(current.balance_staked_root) / taoDivider,
      balance_staked_root_percent: balancePercentOfTotal(
        current.balance_staked_root
      ),
      balance_staked_alpha:
        Number.parseFloat(current.balance_staked_alpha_as_tao) / taoDivider,
      balance_staked_alpha_percent: balancePercentOfTotal(
        current.balance_staked_alpha_as_tao
      ),
      balance_free: Number.parseFloat(current.balance_free) / taoDivider,
      balance_free_percent: balancePercentOfTotal(current.balance_free),
      balance_total: Number.parseFloat(current.balance_total) / taoDivider,
      balance_total_usd:
        (Number.parseFloat(current.balance_total) * Number(latestPrice)) /
        taoDivider,
      balance_total_change:
        balanceChange(current.balance_total, current.balance_total_24hr_ago) /
        taoDivider,
      balance_total_change_percent: balanceChangePercent(
        current.balance_total,
        current.balance_total_24hr_ago
      ),
      balance_total_change_usd:
        (balanceChange(current.balance_total, current.balance_total_24hr_ago) *
          Number(latestPrice)) /
        taoDivider,
      rank: `${current.rank}`,
      block_number: `${current.block_number}`,
      createdAt: current.created_on_date
        ? utc(current.created_on_date).format('DD MMM YYYY')
        : '',
      createdNetwork: `${current.created_on_network
        .charAt(0)
        .toUpperCase()}${current.created_on_network.slice(1)} Chain`,
      swap: getColdkeySwapData(id, current.coldkey_swap),
    };
  }, [data, latestPrice, id]);

  const formatUSD = (value: number) =>
    value.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });

  if (!wallet) {
    return (
      <div className='flex w-full flex-col gap-6 px-6 pt-2 lg:px-8'>
        <SpinnerDiv />
        <Skeleton className='h-[300px]' />
      </div>
    );
  }

  return (
    <div className='w-full gap-6 px-6 lg:px-8'>
      <div className='flex flex-col gap-6 lg:flex-row'>
        <AccountCard
          title='Balance'
          icon={<Wallet size={16} />}
          tag={`Rank: #${wallet.rank}`}
          className='flex-[2]'
          isPending={false}
        >
          <div className='flex flex-1 flex-col overflow-hidden'>
            <div className='mb-1 flex flex-row flex-wrap gap-2 overflow-hidden'>
              <PriceFormatter
                data={wallet.balance_total}
                variant='LongPrice'
                prefix={
                  <Bittensor className='-mx-2 -mb-1.5 text-2xl xl:-mx-5 xl:-mb-[1.1rem] xl:text-5xl' />
                }
                mainClassName='!text-3xl xl:!text-5xl'
                contentClassName='!text-2xl xl:!text-4xl'
              />
              <TrendIcon
                isPositive={wallet.balance_total_change >= 0}
                text={`${wallet.balance_total_change_percent.toLocaleString(
                  'en-US',
                  {
                    minimumFractionDigits: 0,
                    maximumFractionDigits: 2,
                  }
                )}%`}
              />
            </div>
            <Text level='lg' className='text-neutral-400'>
              ${formatUSD(wallet.balance_total_usd)}
            </Text>
          </div>
          <div className='w-full'>
            <div className='flex flex-row items-center gap-2'>
              <Text level='sm' className='font-medium opacity-60'>
                Delegated
              </Text>
              <PriceFormatter
                data={wallet.balance_staked}
                variant='Price'
                prefix={<Bittensor className='-mx-1.75 -mb-[0.1rem] text-lg' />}
                mainClassName='text-lg'
                contentClassName='text-lg'
              />
            </div>
            <div className='my-1 flex flex-row gap-1'>
              {wallet.balance_staked_root_percent > 0 && (
                <div
                  className='h-3 min-w-3 rounded-full bg-[#008F7A]'
                  style={{ width: `${wallet.balance_staked_root_percent}%` }}
                />
              )}

              {wallet.balance_staked_alpha_percent > 0 && (
                <div
                  className='bg-ocean h-3 min-w-3 rounded-full'
                  style={{ width: `${wallet.balance_staked_alpha_percent}%` }}
                />
              )}

              {wallet.balance_free_percent > 0 && (
                <div
                  className='h-3 min-w-3 rounded-full bg-[#EB5347]'
                  style={{ width: `${wallet.balance_free_percent}%` }}
                />
              )}

              {wallet.balance_staked_root_percent +
                wallet.balance_staked_alpha_percent +
                wallet.balance_free_percent ===
                0 && (
                <div
                  className='h-3 min-w-3 rounded-full bg-[#1D1D1D]'
                  style={{ width: '100%' }}
                />
              )}
            </div>

            <div className='my-1 flex w-full flex-row items-start justify-between'>
              <div className='flex flex-col'>
                <div className='flex flex-row gap-1'>
                  <PriceFormatter
                    data={wallet.balaced_staked_root}
                    variant='Price'
                    prefix={
                      <>
                        <span className='flex flex-row items-center gap-1'>
                          <span className='flex h-2 w-2 justify-center rounded-full bg-[#008F7A] align-middle' />
                          <span className='text-sm font-medium leading-6 opacity-60'>
                            Root
                          </span>
                        </span>
                        <Bittensor className='-mb-[0.1rem] -ml-1 -mr-1.5 text-lg' />
                      </>
                    }
                    mainClassName='text-lg'
                    contentClassName='text-lg'
                  />
                </div>

                <div className='flex flex-row gap-1'>
                  <PriceFormatter
                    data={wallet.balance_staked_alpha}
                    variant='Price'
                    prefix={
                      <>
                        <span className='flex flex-row items-center gap-1'>
                          <span className='bg-ocean flex h-2 w-2 justify-center rounded-full align-middle' />
                          <span className='text-sm font-medium leading-6 opacity-60'>
                            Alpha
                          </span>
                        </span>
                        <Bittensor className='-mb-[0.1rem] -ml-1 -mr-1.5 text-lg' />
                      </>
                    }
                    mainClassName='text-lg'
                    contentClassName='text-lg'
                  />
                </div>
              </div>

              <div className='flex flex-row justify-end gap-1'>
                <PriceFormatter
                  data={wallet.balance_free}
                  variant='Price'
                  prefix={
                    <>
                      <span className='flex flex-row items-center gap-1'>
                        <span className='h-2 w-2 justify-center rounded-full bg-[#EB5347] align-middle' />
                        <span className='text-sm font-medium leading-6 opacity-60'>
                          Free
                        </span>
                      </span>
                      <Bittensor className='-mb-[0.1rem] -ml-1 -mr-1.5 text-lg' />
                    </>
                  }
                  mainClassName='text-lg'
                  contentClassName='text-lg'
                />
              </div>
            </div>
          </div>
        </AccountCard>

        <div className='flex flex-1 flex-col gap-6 sm:max-lg:flex-row'>
          <AccountCard
            icon={<ChartSpline size={14} />}
            title='24h Balance Change'
            className='min-h-[8.75rem]'
            isPending={false}
          >
            <div className='flex flex-row items-end gap-4'>
              <PriceFormatter
                data={wallet.balance_total_change}
                variant='Currency'
                prefix={<Bittensor className='-m-3 mt-0 text-4xl' />}
                mainClassName='text-4xl'
                contentClassName='text-2xl'
              />
              <TrendIcon isPositive={wallet.balance_total_change >= 0} />
            </div>
            <Text level='lg' className='text-neutral-400'>
              ${formatUSD(wallet.balance_total_change_usd)}
            </Text>
          </AccountCard>

          <AccountCard
            icon={<Calendar size={14} />}
            title='Created On'
            className='min-h-[8.75rem]'
            isPending={false}
          >
            <Text level='mdTitle'>{wallet.createdAt}</Text>
            <Text className='text-neutral-400'>{wallet.createdNetwork}</Text>
          </AccountCard>
        </div>

        <div className='flex flex-1 flex-col'>
          <div className='ml-2 flex flex-col items-start justify-end sm:ml-5 lg:flex-row'>
            <Link href={appRoutes.blockchain.heightDetail(wallet.block_number)}>
              <p className='mb-2 text-xs font-thin'>Last updated at Block:</p>
              <div className='flex items-center gap-2 pb-8'>
                <Text
                  level='xl'
                  className='text-ocean decoration-ocean flex flex-1 items-center gap-2 tracking-normal hover:underline'
                >
                  <BiCubeAlt />
                  {wallet.block_number}
                </Text>
              </div>
            </Link>
          </div>

          <div className='ml-2 flex flex-col gap-4 border-t border-neutral-700 py-4 sm:ml-5'>
            <div className='flex flex-col items-start gap-2'>
              <Text level='xs' className='self-start font-thin opacity-70'>
                Address:
              </Text>
              <div className='flex items-center gap-2'>
                <Text
                  level='xs'
                  className='line-clamp-1 break-all font-mono tracking-normal'
                >
                  {((windowSize.width ?? 0) > 1780 ||
                    (windowSize.width ?? 0) < 1024) &&
                  (windowSize.width ?? 0) > 460
                    ? wallet.id
                    : walletFormat(wallet.id)}
                </Text>
                <CopyButton size={14} value={wallet.id} />
              </div>
            </div>
            <div className='flex w-full flex-col gap-2'>
              <p className='flex text-xs font-thin opacity-70'>Public Key</p>
              <div className='flex items-center gap-2'>
                <Text
                  level='xs'
                  className='line-clamp-1 break-all font-mono tracking-normal'
                >
                  {((windowSize.width ?? 0) > 2285 ||
                    (windowSize.width ?? 0) < 1024) &&
                  (windowSize.width ?? 0) > 580
                    ? wallet.publicKey
                    : walletFormat(wallet.publicKey)}
                </Text>
                <CopyButton value={wallet.publicKey} size={14} />
              </div>
            </div>
            {wallet.swap ? (
              <div className='flex w-full flex-col gap-2'>
                <Text level='xs' className='flex text-xs font-thin opacity-70'>
                  {wallet.swap.label}
                </Text>
                <div className='flex items-center gap-2'>
                  <Link
                    href={appRoutes.blockchain.accountDetail(wallet.swap.value)}
                  >
                    <Text
                      level='xs'
                      className='line-clamp-1 cursor-pointer break-all font-mono tracking-normal hover:underline'
                    >
                      {((windowSize.width ?? 0) > 2285 ||
                        (windowSize.width ?? 0) < 1024) &&
                      (windowSize.width ?? 0) > 580
                        ? wallet.swap.value
                        : walletFormat(wallet.swap.value)}
                    </Text>
                  </Link>
                  <CopyButton value={wallet.swap.value} size={14} />
                </div>
              </div>
            ) : null}
          </div>
        </div>
      </div>
    </div>
  );
}
