import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { DtaoWrapper } from '@/components/views/dtao';

export const BalancesTaoCell = (
  context: CellContext<TableData, unknown>,
  currency: 'TAO' | 'USD'
) => (
  <DtaoWrapper
    info={context.row.original.balance_as_tao as number}
    variant={currency === 'USD' ? 'MarketCap' : 'Stake'}
    currentUSD={currency === 'USD'}
    className='w-30 flex justify-end'
  />
);

export const BalancesTaoHeader = (currency: 'TAO' | 'USD') => (
  <p className='flex w-[calc(120px-1em)] justify-end'>Balance {currency}</p>
);
