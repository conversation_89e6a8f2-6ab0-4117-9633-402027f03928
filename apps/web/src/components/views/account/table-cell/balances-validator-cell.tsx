import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { AddressFormatter } from '@repo/ui/components';

export const BalancesValidatorCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <AddressFormatter
    info={row.original.hotkey as string}
    isEncoded
    isHotkey
    noIcon
    max={12}
    className='text-sm'
  />
);
