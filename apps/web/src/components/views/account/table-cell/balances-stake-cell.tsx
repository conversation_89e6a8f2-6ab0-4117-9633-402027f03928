import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';

export const BalancesStakeCell = ({ row }: CellContext<TableData, unknown>) => (
  <TableText
    info={row.original.percent_of_staked as string}
    percentage
    className='w-30 flex justify-end'
  />
);

export const BalancesStakeHeader = () => (
  <p className='flex w-[calc(120px-1em)] justify-end'>% of Stake</p>
);
