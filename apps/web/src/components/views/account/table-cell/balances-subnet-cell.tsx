import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { SubnetNameDisplay } from '@repo/ui/components';

export const BalancesSubnetCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <SubnetNameDisplay
    isClickable
    subnetName={row.original.subnet_name as string}
    netuid={row.original.netuid as number}
    className='w-52'
  />
);
