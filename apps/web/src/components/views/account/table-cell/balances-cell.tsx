import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { DtaoWrapper } from '@/components/views/dtao';

export const BalancesCell = ({ row }: CellContext<TableData, unknown>) => (
  <DtaoWrapper
    info={row.original.balance as number}
    maximumFractionDigits={4}
    minimumFractionDigits={0}
    suffix={
      <span className='mr-1'>{row.original.subnet_symbol as string}</span>
    }
    className='w-30 flex justify-end'
    variant='Stake'
  />
);

export const BalancesHeader = () => (
  <p className='flex w-[calc(120px-1em)] justify-end'>Balance</p>
);
