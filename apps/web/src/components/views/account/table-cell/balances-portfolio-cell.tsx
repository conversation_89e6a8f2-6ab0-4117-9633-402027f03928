import type { CellContext } from '@tanstack/react-table';
import { useRouter } from 'next/navigation';
import { CgArrowRight } from 'react-icons/cg';
import type { TableData } from '@repo/types/website-api-types';
import { Button } from '@repo/ui/components';
import { createUrl } from '@repo/ui/lib';
// import { env } from '@/lib/config';

export const BalancesPortfolioCell = ({
  row,
}: CellContext<TableData, unknown>) => {
  const router = useRouter();
  return (
    <Button
      className='flex border-white/40 text-sm'
      variant='cta3'
      onClick={() => {
        router.push(
          createUrl(
            // env.NEXT_PUBLIC_DASHBOARD_BASE_URL,
            '',
            `/portfolio/${row.original.coldkey as string}/${Number(row.original.netuid)}`
          )
        );
      }}
    >
      Data <CgArrowRight className='ml-1' />
    </Button>
  );
};
