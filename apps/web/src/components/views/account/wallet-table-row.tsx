import { useState } from 'react';
import { type Row as TableRow, flexRender } from '@tanstack/react-table';
import { motion } from 'framer-motion';
import { BiChevronDown, BiChevronUp } from 'react-icons/bi';
import { GoGraph } from 'react-icons/go';
import type { TableData } from '@repo/types/website-api-types';
import { cn } from '@repo/ui/lib';

interface RowProps {
  row: TableRow<TableData>;
  i: number;
  pageSize?: number;
  latestHighlight?: boolean;
  small?: boolean;
  isFocus?: boolean;
  tableName?: string;
  colors?: string[];
  subnetColors?: Record<number, number>;
  onClick?: (event: React.MouseEvent<HTMLTableRowElement>) => void;
  link?: boolean;
}

export const WalletTableRow = ({ row, i, pageSize = 50, small }: RowProps) => {
  const [open, setOpen] = useState<boolean>(false);

  return (
    <>
      <motion.tr
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5, delay: (i % pageSize) * 0.01 }}
        className={cn(
          'overflow-hidden rounded-lg text-xs',
          'bg-neutral-800/20 hover:bg-neutral-800 [&:hover>td]:bg-neutral-800'
        )}
      >
        {row.getVisibleCells().map((cell, index) => (
          <td
            className={cn(
              small && '!h-10',
              'h-14 cursor-default px-2 text-left text-white',
              'overflow-hidden first:rounded-l-lg first:pl-5 last:rounded-r-lg last:pr-5'
            )}
            key={index}
          >
            <div className='flex flex-row items-center gap-1'>
              {flexRender(cell.column.columnDef.cell, cell.getContext())}
              {cell.column.id === 'graph' ? (
                cell.row.original.hotkey !== undefined ? (
                  open ? (
                    <div className='flex flex-row gap-1'>
                      <GoGraph
                        size={18}
                        className='cursor-pointer'
                        onClick={() => {
                          setOpen(!open);
                        }}
                      />
                      <BiChevronUp
                        size={16}
                        className='cursor-pointer'
                        onClick={() => {
                          setOpen(!open);
                        }}
                      />
                    </div>
                  ) : (
                    <div className='flex flex-row gap-1'>
                      <GoGraph
                        size={18}
                        className='cursor-pointer'
                        onClick={() => {
                          setOpen(!open);
                        }}
                      />
                      <BiChevronDown
                        size={16}
                        className='cursor-pointer'
                        onClick={() => {
                          setOpen(!open);
                        }}
                      />
                    </div>
                  )
                ) : null
              ) : null}
            </div>
          </td>
        ))}
      </motion.tr>

      {open ? (
        <tr>
          <td colSpan={row.getVisibleCells().length}>
            <StakeBalanceHistoryChartDynamic
              id={row.original.coldkey as string}
              netuid={row.original.netuid as number}
              subnetSymbol={row.original.subnet_symbol as string}
              hotkey={row.original.hotkey as string}
              currency={row.original.currency as CurrencySelectionOptions}
            />
          </td>
        </tr>
      ) : null}
    </>
  );
};
