'use client';

import { useEffect, useMemo, useState } from 'react';
import { usePathname } from 'next/navigation';
import { Link } from '@repo/ui/components';
import { appRoutes, cn } from '@repo/ui/lib';

export default function AccountTabs({ path }: { path: string }) {
  const subTabs = useMemo(
    () => [
      { name: 'Balances', href: appRoutes.blockchain.accountDetail(path) },
      {
        name: 'Transactions',
        href: appRoutes.blockchain.accountTransactions(path),
      },
      { name: 'Transfers', href: appRoutes.blockchain.accountTransfer(path) },
      {
        name: 'Extrinsics',
        href: appRoutes.blockchain.accountExtrinsics(path),
      },
    ],
    [path]
  );

  const pathname = usePathname();
  const [currentTab, setCurrentTab] = useState<string>(subTabs[0].name);

  useEffect(() => {
    for (const { name, href } of subTabs.toReversed()) {
      if (pathname.includes(href)) {
        setCurrentTab(name);
        return;
      }
    }

    setCurrentTab(subTabs[0].name);
  }, [pathname, subTabs]);

  return (
    <div className='flex gap-0 self-start sm:gap-2'>
      {subTabs.map((item) => (
        <Link
          key={item.name}
          className={cn(
            'text-md flex cursor-pointer items-center gap-3 rounded-xl px-3 py-2 hover:bg-neutral-900 sm:px-3 md:text-base md:leading-4',
            currentTab === item.name
              ? 'bg-white/10'
              : 'border-transparent text-neutral-500'
          )}
          href={item.href}
        >
          {item.name}
        </Link>
      ))}
    </div>
  );
}
