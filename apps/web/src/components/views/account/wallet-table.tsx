'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import type { CellContext, SortingState } from '@tanstack/react-table';
import type {
  ColumnSchema,
  DtaoSubnetsAPI,
  StakeBalance,
  TableData,
} from '@repo/types/website-api-types';
import { BubbleTable, TaoSwitch } from '@repo/ui/components';
import {
  BalancesCell,
  BalancesHeader,
  BalancesStakeCell,
  BalancesStakeHeader,
  BalancesSubnetCell,
  BalancesTaoCell,
  BalancesTaoHeader,
  BalancesValidatorCell,
} from './table-cell';
import { BalancesPortfolioCell } from './table-cell/balances-portfolio-cell';
import TablePagination from '@/components/views/blockchain/table-pagination';
import { useAccount } from '@/lib/hooks';
import { getSorting } from '@/lib/utils';

export const DtaoWalletTable = ({
  coldkey,
  subnets,
  stakeBalanceData,
}: {
  coldkey: string;
  subnets: DtaoSubnetsAPI | null;
  stakeBalanceData: StakeBalance[] | null;
}) => {
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(10);
  const [total, setTotal] = useState<number>(0);
  const [sorting, setSorting] = useState<SortingState>([
    { id: 'balance_as_tao', desc: true },
  ]);
  const [currency, setCurrency] = useState<'TAO' | 'USD'>('TAO');
  const { data: addressData, isPending } = useAccount({ address: coldkey });

  const column = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'netuid',
        header: 'Subnet',
        cell: BalancesSubnetCell,
      },
      {
        id: 'hotkey',
        header: 'Validator',
        cell: BalancesValidatorCell,
      },
      {
        id: 'graph',
        enableSorting: false,
      },
      {
        id: 'percent_of_staked',
        header: BalancesStakeHeader,
        cell: BalancesStakeCell,
      },
      {
        id: 'balance',
        header: BalancesHeader,
        cell: BalancesCell,
      },
      {
        id: 'balance_as_tao',
        header: () => BalancesTaoHeader(currency),
        cell: (info: CellContext<TableData, unknown>) =>
          BalancesTaoCell(info, currency),
      },
      {
        id: 'portfolio_link',
        cell: BalancesPortfolioCell,
        enableSorting: false,
      },
    ],
    [currency]
  );

  const amountStaked = useMemo(
    () => addressData?.data[0]?.balance_staked,
    [addressData?.data]
  );

  const tableData = useMemo<TableData[]>(() => {
    const findSubnet = (netuid: number) =>
      subnets?.data.find((subnet) => subnet.netuid === netuid);

    const percentStaked = (value: string) => {
      if (!amountStaked) {
        return 0;
      }

      return (Number(value) / Number(amountStaked)) * 100;
    };

    return (
      stakeBalanceData
        ?.map(({ netuid, balance, balance_as_tao: balanceAsTao, hotkey }) => ({
          rowkey: `${coldkey}-${netuid}-${hotkey.ss58}`,
          coldkey,
          netuid,
          balance: Number(balance),
          balance_as_tao: Number(balanceAsTao),
          hotkey: hotkey.ss58,
          subnet_symbol: findSubnet(netuid)?.symbol || '',
          subnet_name: findSubnet(netuid)?.subnet_name || '',
          subnet_description: findSubnet(netuid)?.subnet_description || '',
          percent_of_staked: percentStaked(balanceAsTao),
          currency,
        }))
        .sort((a, b) => getSorting(a, b, sorting))
        .slice((page - 1) * limit, page * limit) ?? []
    );
  }, [
    stakeBalanceData,
    page,
    limit,
    subnets,
    amountStaked,
    coldkey,
    sorting,
    currency,
  ]);

  const handlePageChange = useCallback((value: number) => {
    setPage(value);
  }, []);

  const handleLimitChange = useCallback((value: number) => {
    setLimit(value);
    setPage(1);
  }, []);

  const handleFilterChange = useCallback(
    (_: string, sortingValue: SortingState) => {
      setSorting(sortingValue);
      setPage(1);
    },
    []
  );

  useEffect(() => {
    setTotal(stakeBalanceData?.length ?? 0);
  }, [stakeBalanceData]);

  return (
    <div className='w-full gap-6' id='transaction-table'>
      <BubbleTable
        columnSchemas={column}
        rowData={tableData}
        onLimitChange={handleLimitChange}
        onFilterChange={handleFilterChange}
        selector
        csvExport={false}
        taoSwitch={
          <TaoSwitch
            selection={currency}
            onSelectionChange={(selection) => {
              setCurrency(selection);
            }}
          />
        }
        variant='Balances'
        initialSortBy={sorting}
        initialPageSize={limit}
        isFetching={isPending}
      />
      <TablePagination
        total={total}
        defaultPageSize={limit}
        currentPage={page}
        onPageChange={handlePageChange}
      />
    </div>
  );
};
