import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TaoWrapper } from '@/components/elements/tao-wrapper';

export const ChildHotkeyDailyTaoCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TaoWrapper
    className='text-fire'
    info={row.original.daily_reward as string}
    number
    minimumFractionDigits={0}
    maximumFractionDigits={4}
  />
);
