import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';
import { taoDivider } from '@repo/ui/lib';

export const ChildHotkeyEmissionCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <TableText
    className='text-neutral-500'
    info={row.original.emission as string}
    dividedBy={taoDivider}
  />
);
