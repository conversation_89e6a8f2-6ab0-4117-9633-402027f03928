import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { AddressFormatter } from '@repo/ui/components';

export const ParentHotkeyCell = ({ row }: CellContext<TableData, unknown>) => (
  <AddressFormatter
    info={row.original.hotkey as string}
    noIcon
    max={15}
    isValidator
    className='!text-sm'
    rootClassName='min-w-40 max-w-40 justify-start'
  />
);
