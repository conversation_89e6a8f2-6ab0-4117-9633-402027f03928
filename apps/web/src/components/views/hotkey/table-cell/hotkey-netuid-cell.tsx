import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { Link, Text } from '@repo/ui/components';
import { appRoutes } from '@repo/ui/lib';

export const HotkeyNetuidCell = ({ row }: CellContext<TableData, unknown>) => (
  <Link href={appRoutes.subnets.detail(row.original.netuid as string)}>
    <Text level='sm' className='text-ocean'>
      {Number(row.original.netuid)}
    </Text>
  </Link>
);
