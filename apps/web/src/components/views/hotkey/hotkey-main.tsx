import type { MetagraphAP<PERSON> } from '@repo/types/website-api-types';
import { HotkeyGraph } from './hotkey-graph';
import { HotkeySubTables } from './hotkey-sub-tables';
import { fetchSubnet } from '@/api-proxy/subnets';

export async function HotkeyMainSection({
  hotkey,
  data,
}: {
  hotkey: string;
  data?: MetagraphAPI;
}) {
  const subnetTempoResponses = await Promise.allSettled(
    data?.data.map(async (item) => fetchSubnet({ netuid: item.netuid })) ?? []
  );

  const subnetTempoData = subnetTempoResponses.flatMap((item) => {
    if (item.status === 'fulfilled') {
      const subnetData = item.value.data[0];
      return {
        subnetId: subnetData.netuid,
        tempo: subnetData.tempo,
      };
    }

    return [];
  });

  return (
    <>
      <HotkeySubTables hotkey={hotkey} data={data} />
      <HotkeyGraph hotkey={hotkey} subnets={subnetTempoData} />
    </>
  );
}
