import type { HotkeyFamily, Metagraph } from '@repo/types/website-api-types';
import { Button, Link, Text } from '@repo/ui/components';
import { appRoutes } from '@repo/ui/lib';
import { ChildHotkeyMetagraphDynamic } from './child-hotkey-metagraph.dynamic';
import { ParentHotkeyTableDynamic } from './parent-hotkey-table.dynamic';

export const ChildHotkeyTable = ({
  data,
  subnetID,
  familyData,
}: {
  data?: Metagraph[];
  subnetID: string;
  familyData?: HotkeyFamily;
}) => {
  return (
    <div className='space-y-2 overflow-x-auto rounded-2xl border-neutral-900 bg-neutral-900 px-2 py-4 sm:space-y-6 md:border md:p-4 md:py-6'>
      <div id={`subnet${Number(subnetID)}`} />
      <Button asChild variant='link' className='text-ocean decoration-ocean'>
        <Link href={appRoutes.subnets.detail(subnetID)}>
          <Text level='mdTitle' className='text-ocean font-medium'>
            Subnet {subnetID}
          </Text>
        </Link>
      </Button>
      <ChildHotkeyMetagraphDynamic data={data} />
      {familyData ? (
        <ParentHotkeyTableDynamic
          tableData={familyData.parent}
          childData={familyData.child}
        />
      ) : null}
    </div>
  );
};
