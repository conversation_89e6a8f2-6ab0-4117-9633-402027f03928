'use client';

import { useEffect, useMemo, useState } from 'react';

import type {
  ColumnSchema,
  MetagraphParamsAtom,
} from '@repo/types/website-api-types';
import { BubbleTable } from '@repo/ui/components';
import { taoDivider } from '@repo/ui/lib';
import { useMetagraph } from '@/lib/hooks';
import { useLatestPriceAtom } from '@/store/use-latest-price';
import {
  HotkeyAxonCell,
  HotkeyDailyCell,
  HotkeyIncentiveCell,
  HotkeyNetuidCell,
  HotkeyPosCell,
  HotkeyTimeCell,
  HotkeyUidCell,
  HotkeyUpdatedCell,
} from './table-cell';

export function HotkeyMetagraphTable({ hotkey }: { hotkey: string }) {
  const [metagraphParams, setMetagraphParams] = useState<MetagraphParamsAtom>(
    {}
  );
  const { data: metagraph, isPending } = useMetagraph(metagraphParams);

  const { latestPrice: taoValue } = useLatestPriceAtom();

  const tableData = useMemo(() => {
    return (
      metagraph?.data
        .sort((a, b) => a.netuid - b.netuid)
        .map(
          ({
            netuid,
            uid,
            stake,
            trust,
            validator_trust: validatorTrust,
            consensus,
            incentive,
            dividends,
            emission,
            active,
            hotkey,
            coldkey,
            axon,
            daily_reward: dailyReward,
            is_immunity_period: isImmunityPeriod,
            updated,
            rank,
            timestamp,
          }) => ({
            netuid,
            uid,
            stake: Number(stake) / taoDivider,
            total: (Number(stake) * taoValue) / taoDivider,
            trust: Number(trust),
            validator_trust: Number(validatorTrust),
            type: Number(validatorTrust) > 0 ? 'VALIDATOR' : 'MINER',
            consensus: Number(consensus),
            incentive,
            dividends: Number(dividends),
            emission: Number(emission),
            active,
            hotkey: hotkey.ss58,
            coldkey: coldkey.ss58,
            updated,
            axon: axon?.ip ? `${axon.ip}:${axon.port}` : '0.0.0.0',
            daily_reward: Number(dailyReward) / taoDivider,
            daily_reward1: (Number(dailyReward) * taoValue) / taoDivider,
            is_immunity_period: isImmunityPeriod,
            position: rank,
            timestamp,
          })
        ) ?? []
    );
  }, [metagraph, taoValue]);

  const columns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'netuid',
        header: 'Subnet ID',
        cell: HotkeyNetuidCell,
        enableSorting: false,
      },
      {
        id: 'position',
        header: 'POS',
        cell: HotkeyPosCell,
        enableSorting: false,
      },
      {
        id: 'uid',
        header: 'UID',
        cell: HotkeyUidCell,
      },
      {
        id: 'daily_reward',
        header: 'Daily 𝞃',
        cell: HotkeyDailyCell,
      },
      {
        id: 'incentive',
        header: 'Incentive',
        cell: HotkeyIncentiveCell,
      },
      {
        id: 'updated',
        header: 'Updated',
        cell: HotkeyUpdatedCell,
      },
      {
        id: 'axon',
        header: 'Axon',
        cell: HotkeyAxonCell,
      },
      {
        id: 'timestamp',
        header: 'Last Updated (UTC)',
        cell: HotkeyTimeCell,
        enableSorting: false,
      },
    ],
    []
  );

  useEffect(() => {
    setMetagraphParams({ hotkey });
  }, [hotkey, setMetagraphParams]);

  return (
    <BubbleTable
      columnSchemas={columns}
      rowData={tableData}
      isManual={false}
      isFetching={isPending}
    />
  );
}
