'use client';

import { useEffect } from 'react';
import { AddressFormatter, CopyButton, Link, Text } from '@repo/ui/components';
import { appRoutes } from '@repo/ui/lib';
import { ChildHotkeyHeader } from './child-hotkey-header';

export const HotkeyHeader = ({
  data,
  isChildHotkey,
  hotkey,
}: {
  data: {
    label: string;
    value: string;
  }[];
  isChildHotkey?: boolean;
  hotkey: string;
}) => {
  useEffect(() => {
    const hash = window.location.hash.substring(1);

    if (hash) {
      // Use the standard DOM method to find the element by ID
      const element = document.getElementById(hash);
      if (element) {
        // Calculate the position you want to scroll to
        const elementPosition =
          element.getBoundingClientRect().top + window.pageYOffset;
        const offsetPosition = elementPosition - 150; // Adjust for offset

        // Scroll to the element with the offset
        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth',
        });
      }
    }
  }, []);

  if (isChildHotkey) {
    return <ChildHotkeyHeader data={data} hotkey={hotkey} />;
  }

  return (
    <div className='flex flex-col gap-2 pt-6'>
      {data.map(({ label, value }) => (
        <div
          className='flex h-max w-full flex-col items-start gap-2 rounded-xl border border-[#CBCBCB1A] bg-[#161616] px-6 py-2 sm:w-fit sm:flex-row sm:items-center'
          key={label}
        >
          <Text level='md' className='w-52 text-[#777777]'>
            {label}
          </Text>
          {label === 'Hotkey' ? (
            <AddressFormatter
              uid={value}
              fullWidth
              noIcon
              className='text-sm'
            />
          ) : (
            <Link href={appRoutes.subnets.coldkey(value)}>
              <Text
                level='md'
                className='flex items-center gap-1 break-all hover:underline'
              >
                {value}
                <CopyButton value={value} size={14} className='ml-2' />
              </Text>
            </Link>
          )}
        </div>
      ))}
    </div>
  );
};
