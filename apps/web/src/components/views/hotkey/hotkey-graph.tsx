'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { LinearGradient } from '@visx/gradient';
import {
  AreaSeries,
  Axis,
  buildChartTheme,
  LineSeries,
  Tooltip,
  XYChart,
} from '@visx/xychart';
import { utc } from 'moment';
import { format } from 'numerable';
import type { ValidatorPerformanceQueryParams } from '@repo/types/website-api-types';
import { Separator, Skeleton, Text } from '@repo/ui/components';
import { cn, taoDivider, useWindowSize } from '@repo/ui/lib';
import type { DataPoint } from '@/components/views/subnets/registration-cost-chart';
import { useValidatorPerformanceHistory } from '@/lib/hooks';
import { Bittensor } from '@/components/icons/bittensor';

const accessors = {
  xAccessor: (d: DataPoint) => new Date(d.x),
  yAccessor: (d: DataPoint) => d.y,
};

export function HotkeyGraph({
  hotkey,
  subnets,
}: {
  hotkey: string;
  subnets: {
    subnetId: number;
    tempo: number;
  }[];
}) {
  const finalSubnets = subnets.filter((item) => item.subnetId !== 0);

  const [selectedSubnet, setSelectedSubnet] = useState<number>(
    finalSubnets[0]?.subnetId
  );
  const [
    validatorPerformanceHistoryParams,
    setValidatorPerformanceHistoryParams,
  ] = useState<ValidatorPerformanceQueryParams>({
    hotkey,
    netuid: selectedSubnet,
  });

  const { data: histories, isPending } = useValidatorPerformanceHistory(
    validatorPerformanceHistoryParams
  );

  const data = useMemo(
    () => histories?.filter((item) => item.timestamp.length > 0) ?? [],
    [histories]
  );

  const emissionData = data.map((item) => ({
    x: item.timestamp,
    y: Number(item.emission) / taoDivider,
    oldY: Number(item.emission) / taoDivider,
  }));
  const updatedData = data.map((item) => ({
    x: item.timestamp,
    y: Number(item.blocks_since_weights_set),
    oldY: Number(item.blocks_since_weights_set),
  }));

  const tempoData = data.map((item) => ({
    x: item.timestamp,
    y: finalSubnets.find((item) => item.subnetId === selectedSubnet)?.tempo,
    oldY: finalSubnets.find((item) => item.subnetId === selectedSubnet)?.tempo,
  }));

  const [emissionMin, emissionMax] = useMemo(() => {
    const max = Math.max(...emissionData.map((d) => d.y));
    const min = Math.min(...emissionData.map((d) => d.y));

    return [min, max];
  }, [emissionData]);

  const [updatedMin, updatedMax] = useMemo(() => {
    const subnetTempo =
      finalSubnets.find((item) => item.subnetId === selectedSubnet)?.tempo ?? 0;
    const min = Math.min(...updatedData.map((d) => d.y), subnetTempo);

    return [min, 1000];
  }, [updatedData, finalSubnets, selectedSubnet]);

  const convertUpdateTickToEmissionScale = useCallback(
    (tick: number) => {
      const emissionDomain = emissionMax - emissionMin;
      const updatedDomain = updatedMax - updatedMin;

      const convertedTick =
        ((tick - updatedMin) / updatedDomain) * emissionDomain + emissionMin;

      return format(Math.max(0, convertedTick), '0.000');
    },
    [emissionMin, emissionMax, updatedMin, updatedMax]
  );

  const convertedEmissionData = useMemo(() => {
    const emissionDomain = emissionMax - emissionMin;
    const updatedDomain = updatedMax - updatedMin;

    return emissionData.map((item) => {
      const convertedData =
        ((item.y - emissionMin) / emissionDomain) * updatedDomain + updatedMin;
      return {
        x: item.x,
        y: convertedData,
        oldY: convertedData,
      };
    });
  }, [emissionMax, emissionMin, updatedMax, updatedMin, emissionData]);

  const customTheme = buildChartTheme({
    backgroundColor: '',
    colors: ['#04ad96', '#F90'],
    gridColor: '#e8e8e8',
    gridColorDark: '#222831',
    tickLength: 1,
  });

  const isMobile = useWindowSize().isMobile;

  useEffect(() => {
    setValidatorPerformanceHistoryParams({
      hotkey,
      netuid: selectedSubnet,
    });
  }, [selectedSubnet, hotkey, setValidatorPerformanceHistoryParams]);

  return (
    <div className='flex flex-col gap-9 rounded-2xl border border-neutral-800 bg-neutral-900 p-6 shadow-lg md:p-12'>
      <div className='flex flex-wrap gap-0 self-start sm:gap-2'>
        {finalSubnets
          .sort((a, b) => a.subnetId - b.subnetId)
          .map((item) => (
            <Text
              key={item.subnetId}
              className={cn(
                'text-md flex cursor-pointer items-center gap-3 rounded-xl px-3 py-2 hover:bg-neutral-800 sm:px-3 md:text-base md:leading-4',
                selectedSubnet === item.subnetId
                  ? 'bg-white/10'
                  : 'border-transparent text-neutral-500'
              )}
              onClick={() => {
                setSelectedSubnet(item.subnetId);
              }}
            >
              {`SN${item.subnetId}`}
            </Text>
          ))}
      </div>
      {isPending ? (
        <Skeleton className='h-96 w-full' />
      ) : (
        <XYChart
          height={400}
          xScale={{ type: 'time' }}
          yScale={{
            type: 'linear',
            domain: [updatedMin, updatedMax],
            zero: false,
          }}
          margin={{
            top: 20,
            right: isMobile ? 40 : 100,
            bottom: 40,
            left: isMobile ? 40 : 90,
          }}
          theme={customTheme}
        >
          <defs>
            <LinearGradient
              id='gradient'
              from='#00DBBC'
              fromOpacity={0.1}
              to='#000000'
              toOpacity={0.01}
              x1='100%'
              y1='0%'
              x2='100%'
              y2='100%'
            />
            <LinearGradient
              id='gradient2'
              from='#F90'
              fromOpacity={0.2}
              to='#000000'
              toOpacity={0.01}
              x1='100%'
              y1='0%'
              x2='100%'
              y2='100%'
            />
          </defs>
          <Axis
            hideAxisLine
            hideTicks
            hideZero
            orientation='right'
            numTicks={8}
            label='Emission (τ)'
            tickFormat={(taoValue: number) =>
              convertUpdateTickToEmissionScale(taoValue)
            }
            labelOffset={isMobile ? 50 : 85}
            labelProps={{
              fill: '#F90',
              fontSize: 12,
              fontWeight: 400,
              fontFamily: 'var(--font-sans)',
            }}
            tickLabelProps={() => ({
              fill: '#F90',
              fontSize: 10,
              fontWeight: 400,
              fontFamily: 'var(--font-sans)',
              dx: isMobile ? '0px' : '18px',
            })}
          />

          <Axis
            hideAxisLine
            hideTicks
            hideZero
            orientation='left'
            numTicks={8}
            label='Updated'
            labelOffset={isMobile ? 50 : 70}
            labelProps={{
              fill: '#00DBBC',
              fontSize: 12,
              fontWeight: 400,
              fontFamily: 'var(--font-sans)',
            }}
            tickLabelProps={() => ({
              fill: '#00DBBC',
              fontSize: 10,
              fontWeight: 400,
              fontFamily: 'var(--font-sans)',
              dx: isMobile ? '0px' : -'18px',
            })}
          />

          <Axis
            hideAxisLine
            hideTicks
            orientation='bottom'
            numTicks={isMobile ? 4 : 7}
            tickFormat={(date: string) => {
              return utc(date).format('DD MMM');
            }}
            tickLabelProps={() => ({
              fill: '#7E7E7E',
              fontSize: 10,
              fontWeight: 300,
              fontFamily: 'var(--font-sans)',
              dy: '10px',
            })}
          />
          <AreaSeries
            dataKey='Line 1'
            data={updatedData.slice().reverse()}
            yAccessor={accessors.yAccessor}
            xAccessor={accessors.xAccessor}
            fill='url(#gradient)'
            strokeWidth={0.8}
            lineProps={{
              strokeWidth: 1,
            }}
          />
          <AreaSeries
            dataKey='Line 2'
            data={convertedEmissionData.slice().reverse()}
            yAccessor={(d) => d.y}
            xAccessor={accessors.xAccessor}
            fill='url(#gradient2)'
            lineProps={{
              strokeWidth: 1,
            }}
          />
          <LineSeries
            dataKey='Line 3'
            data={tempoData.slice().reverse()}
            stroke='#00DBBC'
            strokeDasharray='4 4'
            strokeWidth={1}
            yAccessor={(d) => d.y}
            xAccessor={accessors.xAccessor}
          />
          <Tooltip
            snapTooltipToDatumX
            snapTooltipToDatumY
            showVerticalCrosshair
            showSeriesGlyphs
            verticalCrosshairStyle={{
              stroke: '#00DBBC',
              strokeDasharray: '4 4',
            }}
            renderTooltip={({ tooltipData }) => {
              if (!tooltipData || !tooltipData.nearestDatum) {
                return null;
              }

              const { datum } = tooltipData.nearestDatum;
              const emissionDatum = emissionData.find(
                (d: DataPoint) => d.x === (datum as DataPoint).x
              );
              const updatedDatum = updatedData.find(
                (d: DataPoint) => d.x === (datum as DataPoint).x
              );

              if (!emissionDatum || !updatedDatum) {
                return null;
              }

              return (
                <div className='z-10 flex flex-col gap-2 rounded-xl border border-neutral-800 bg-neutral-800/10 p-4 backdrop-blur-xl'>
                  <Text level='sm' className='font-medium opacity-70'>
                    {utc((datum as DataPoint).x).format('DD MMM YYYY')}
                  </Text>
                  <Separator />
                  <div className='flex flex-col items-start'>
                    <Text level='sm' className='max-w-60 opacity-50'>
                      Updated
                    </Text>
                    <Text
                      className='text-ocean flex items-center'
                      level='mdTitle'
                    >
                      {format(updatedDatum.oldY, '0')}
                    </Text>
                  </div>
                  <div className='flex flex-col items-start'>
                    <Text level='sm' className='max-w-60 opacity-50'>
                      Emission
                    </Text>
                    <Text
                      className='flex items-center text-[#F90]'
                      level='mdTitle'
                    >
                      {format(emissionDatum.oldY, '0.000 a')} <Bittensor />
                    </Text>
                  </div>
                </div>
              );
            }}
          />
        </XYChart>
      )}
    </div>
  );
}
