'use client';

import { useMemo } from 'react';
import { useAtomValue } from 'jotai';
import type { ColumnSchema } from '@repo/types/website-api-types';
import { taoDivider } from '@repo/ui/lib';
import UidWrapper from '@/components/elements/uid-wrapper';
import { BubbleTable } from '@repo/ui/components';

export const ChildHotkeyMetagraph = ({ data }: { data?: Metagraph[] }) => {
  const taoValue = Number(useAtomValue(latestPriceAtom) ?? '0');

  const columns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'neuron_id',
        header: 'UID',
        cell: (info) => <UidWrapper info={info} />,
      },
      {
        id: 'stake',
        header: 'Stake (τ)',
        cell: (info) => <TaoWrapper info={info} className='text-fire' number />,
        sortingFn: 'alphanumeric',
      },
      {
        id: 'validator_trust',
        header: 'VTrust',
        cell: (info) => (
          <TableText u16Percentage className='text-neutral-500' info={info} />
        ),
      },
      {
        id: 'trust',
        header: 'Trust',
        cell: (info) => (
          <TableText className='text-neutral-500' info={info} dividedBy={1} />
        ),
      },
      {
        id: 'consensus',
        header: 'Consensus',
        cell: (info) => (
          <TableText className='text-neutral-500' info={info} dividedBy={1} />
        ),
      },
      {
        id: 'incentive',
        header: 'Incentive',
        cell: (info) => (
          <TableText className='text-neutral-500' info={info} dividedBy={1} />
        ),
      },
      {
        id: 'dividends',
        header: 'Dividends',
        cell: (info) => (
          <TableText u16Percentage className='text-neutral-500' info={info} />
        ),
      },
      {
        id: 'emission',
        header: 'Emission(p)',
        cell: (info) => (
          <TableText
            className='text-neutral-500'
            info={info}
            dividedBy={taoDivider}
          />
        ),
      },
      {
        id: 'updated',
        header: 'Updated',
        cell: (info) => <TableText info={info} />,
      },
      {
        id: 'axon_info',
        header: 'Axon',
        cell: (info) => <TableText info={info} />,
      },
      {
        id: 'hotkey',
        header: 'Hotkey',
        cell: (info) => (
          <AddressFormatter
            info={info}
            noChange
            noIcon
            max={6}
            isHotkey
            className='!text-sm'
          />
        ),
      },
      {
        id: 'coldkey',
        header: 'Coldkey',
        cell: (info) => <KeyWrapper info={info} className='!text-sm' />,
      },
      {
        id: 'daily_reward',
        header: 'Daily 𝞃',
        cell: (info) => (
          <TaoWrapper
            className='text-fire'
            info={info}
            number
            minimumFractionDigits={0}
            maximumFractionDigits={4}
          />
        ),
      },
      {
        id: 'daily_reward1',
        header: 'Daily $',
        cell: (info) => <TaoWrapper info={info} onlyUSD number />,
      },
      {
        id: 'total',
        header: 'Total $',
        cell: (info) => <TaoWrapper info={info} isTotal number />,
      },
    ],
    []
  );

  const tableData = useMemo(() => {
    return (
      data?.map(
        (
          {
            netuid,
            uid,
            stake,
            trust,
            validator_trust,
            consensus,
            incentive,
            dividends,
            emission,
            block_number,
            active,
            hotkey,
            coldkey,
            axon,
            daily_reward,
            is_immunity_period,
            updated,
          },
          index: number
        ) => ({
          subnet_id: netuid,
          neuron_id: uid,
          stake: Number(stake) / taoDivider,
          total: (Number(stake) * taoValue) / taoDivider,
          trust: Number(trust),
          validator_trust: Number(validator_trust),
          consensus: Number(consensus),
          incentive,
          dividends: Number(dividends),
          emission: Number(emission),
          registered_at_block: block_number,
          active,
          hotkey: hotkey.ss58,
          coldkey: coldkey.ss58,
          updated,
          axon_info: axon?.ip ? `${axon.ip}:${axon.port}` : '0.0.0.0',
          daily_reward: Number(daily_reward) / taoDivider,
          daily_reward1: (Number(daily_reward) * taoValue) / taoDivider,
          is_immunity_period,
          position: index + 1,
        })
      ) ?? []
    );
  }, [data, taoValue]);

  return <BubbleTable columnSchemas={columns} rowData={tableData} />;
};
