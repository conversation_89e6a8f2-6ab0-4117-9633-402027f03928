'use client';

import { useMemo } from 'react';
import type { ColumnSchema, Metagraph } from '@repo/types/website-api-types';
import { BubbleTable } from '@repo/ui/components';
import { taoDivider } from '@repo/ui/lib';
import {
  ChildHotkeyAxonCell,
  ChildHotkeyCell,
  ChildHotkeyColdkeyCell,
  ChildHotkeyConsensusCell,
  ChildHotkeyDailyTaoCell,
  ChildHotkeyDailyUsdCell,
  ChildHotkeyDividendsCell,
  ChildHotkeyEmissionCell,
  ChildHotkeyIncentiveCell,
  ChildHotkeyStakeCell,
  ChildHotkeyTotalCell,
  ChildHotkeyTrustCell,
  ChildHotkeyUidCell,
  ChildHotkeyUpdatedCell,
  ChildHotkeyVtrustCell,
} from './table-cell';
import { useLatestPriceAtom } from '@/store/use-latest-price';

export const ChildHotkeyMetagraph = ({ data }: { data?: Metagraph[] }) => {
  const { latestPrice: taoValue } = useLatestPriceAtom();

  const columns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'neuron_id',
        header: 'UID',
        cell: ChildHotkeyUidCell,
      },
      {
        id: 'stake',
        header: 'Stake (τ)',
        cell: ChildHotkeyStakeCell,
        sortingFn: 'alphanumeric',
      },
      {
        id: 'validator_trust',
        header: 'VTrust',
        cell: ChildHotkeyVtrustCell,
      },
      {
        id: 'trust',
        header: 'Trust',
        cell: ChildHotkeyTrustCell,
      },
      {
        id: 'consensus',
        header: 'Consensus',
        cell: ChildHotkeyConsensusCell,
      },
      {
        id: 'incentive',
        header: 'Incentive',
        cell: ChildHotkeyIncentiveCell,
      },
      {
        id: 'dividends',
        header: 'Dividends',
        cell: ChildHotkeyDividendsCell,
      },
      {
        id: 'emission',
        header: 'Emission(p)',
        cell: ChildHotkeyEmissionCell,
      },
      {
        id: 'updated',
        header: 'Updated',
        cell: ChildHotkeyUpdatedCell,
      },
      {
        id: 'axon_info',
        header: 'Axon',
        cell: ChildHotkeyAxonCell,
      },
      {
        id: 'hotkey',
        header: 'Hotkey',
        cell: ChildHotkeyCell,
      },
      {
        id: 'coldkey',
        header: 'Coldkey',
        cell: ChildHotkeyColdkeyCell,
      },
      {
        id: 'daily_reward',
        header: 'Daily 𝞃',
        cell: ChildHotkeyDailyTaoCell,
      },
      {
        id: 'daily_reward1',
        header: 'Daily $',
        cell: ChildHotkeyDailyUsdCell,
      },
      {
        id: 'total',
        header: 'Total $',
        cell: ChildHotkeyTotalCell,
      },
    ],
    []
  );

  const tableData = useMemo(() => {
    return (
      data?.map(
        (
          {
            netuid,
            uid,
            stake,
            trust,
            validator_trust: validatorTrust,
            consensus,
            incentive,
            dividends,
            emission,
            block_number: blockNumber,
            active,
            hotkey,
            coldkey,
            axon,
            daily_reward: dailyReward,
            is_immunity_period: isImmunityPeriod,
            updated,
          },
          index: number
        ) => ({
          subnet_id: netuid,
          neuron_id: uid,
          stake: Number(stake) / taoDivider,
          total: (Number(stake) * taoValue) / taoDivider,
          trust: Number(trust),
          validator_trust: Number(validatorTrust),
          consensus: Number(consensus),
          incentive,
          dividends: Number(dividends),
          emission: Number(emission),
          registered_at_block: blockNumber,
          active,
          hotkey: hotkey.ss58,
          coldkey: coldkey.ss58,
          updated,
          axon_info: axon?.ip ? `${axon.ip}:${axon.port}` : '0.0.0.0',
          daily_reward: Number(dailyReward) / taoDivider,
          daily_reward1: (Number(dailyReward) * taoValue) / taoDivider,
          is_immunity_period: isImmunityPeriod,
          position: index + 1,
        })
      ) ?? []
    );
  }, [data, taoValue]);

  return <BubbleTable columnSchemas={columns} rowData={tableData} />;
};
