'use client';

import { useMemo } from 'react';
import type {
  ChildHotkey,
  ColumnSchema,
  ParentHotkey,
  TableData,
} from '@repo/types/website-api-types';
import { BubbleTable, Text } from '@repo/ui/components';
import {
  ParentHotkeyCell,
  ParentHotkeyChildCell,
  ParentHotkeyProportionCell,
  ParentHotkeyStakeCell,
  ParentHotkeyTakeCell,
} from './table-cell';

export const ParentHotkeyTable = ({
  tableData,
  childData,
}: {
  tableData: ParentHotkey[];
  childData: ChildHotkey | null;
}) => {
  const childColumns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'hotkey',
        header: 'Child',
        cell: ParentHotkeyChildCell,
      },
      {
        id: 'stake',
        header: 'Stake (τ)',
        cell: ParentHotkeyStakeCell,
        sortingFn: 'alphanumeric',
      },
      {
        id: 'take',
        header: 'Take',
        cell: ParentHotkeyTakeCell,
      },
    ],
    []
  );

  const childTableData = useMemo<TableData[]>(
    () => (childData ? [{ ...childData }] : []),
    [childData]
  );

  const parentColumns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'hotkey',
        header: 'Parent',
        cell: ParentHotkeyCell,
      },
      {
        id: 'stake',
        header: 'Stake (τ)',
        cell: ParentHotkeyStakeCell,
        sortingFn: 'alphanumeric',
      },
      {
        id: 'proportion',
        header: 'Proportion',
        cell: ParentHotkeyProportionCell,
      },
    ],
    []
  );

  const parentTableData = useMemo<TableData[]>(
    () => tableData.map((item) => ({ ...item })),
    [tableData]
  );

  return (
    <>
      <div className='space-y-1'>
        <Text level='xsTitle' className='px-3'>
          Hotkeys
        </Text>
        <BubbleTable columnSchemas={childColumns} rowData={childTableData} />
      </div>
      <BubbleTable columnSchemas={parentColumns} rowData={parentTableData} />
    </>
  );
};
