"use client";

import { Text } from "@/components/elements/typography";
import { BubbleTable } from "@/components/ui/bubble-table";
import { AddressFormatter } from "@/lib/utils/address/address-formatter";
import { TableText } from "@/lib/utils/table/table-text";
import { TaoWrapper } from "@/lib/utils/tao/tao-wrapper";
import type { TableData } from "@/types";
import type { ChildHotkey, ColumnSchema, ParentHotkey } from "@/types";
import type { ColumnDef } from "@tanstack/react-table";
import { useMemo } from "react";

export const ParentHotkeyTable = ({
	tableData,
	childData,
}: {
	tableData: ParentHotkey[];
	childData: ChildHotkey | null;
}) => {
	const childColumns: ColumnDef<any, any>[] = useMemo<ColumnSchema[]>(
		() => [
			{
				id: "hotkey",
				header: "Child",
				cell: (info) => (
					<AddressFormatter
						info={info}
						noIcon
						max={15}
						isValidator
						className="!text-sm"
						rootClassName="min-w-40 max-w-40 justify-start"
					/>
				),
			},
			{
				id: "stake",
				header: "Stake (τ)",
				cell: (info) => (
					<TaoWrapper
						info={info}
						className="text-fire md:w-36 md:max-w-36"
						number
					/>
				),
				sortingFn: "alphanumeric",
			},
			{
				id: "take",
				header: "Take",
				cell: (info) => <TableText info={info} className="md:w-36" />,
			},
		],
		[]
	);

	const childTableData = useMemo<TableData[]>(
		() => (childData ? [{ ...childData }] : []),
		[childData]
	);

	const parentColumns: ColumnDef<any, any>[] = useMemo<ColumnSchema[]>(
		() => [
			{
				id: "hotkey",
				header: "Parent",
				cell: (info) => (
					<AddressFormatter
						info={info}
						noIcon
						max={15}
						isValidator
						className="!text-sm"
						rootClassName={"min-w-40 max-w-40 justify-start"}
					/>
				),
			},
			{
				id: "stake",
				header: "Stake (τ)",
				cell: (info) => (
					<TaoWrapper info={info} className="w-36 max-w-36 text-fire" number />
				),
				sortingFn: "alphanumeric",
			},
			{
				id: "proportion",
				header: "Proportion",
				cell: (info) => <TableText info={info} className="w-36" />,
			},
		],
		[]
	);

	const parentTableData = useMemo<TableData[]>(
		() => tableData.map((item) => ({ ...item })),
		[tableData]
	);

	return (
		<>
			<div className="space-y-1">
				<Text level={"xsTitle"} className="px-3">
					Hotkeys
				</Text>
				<BubbleTable columnSchemas={childColumns} rowData={childTableData} />
			</div>
			<BubbleTable columnSchemas={parentColumns} rowData={parentTableData} />
		</>
	);
};
