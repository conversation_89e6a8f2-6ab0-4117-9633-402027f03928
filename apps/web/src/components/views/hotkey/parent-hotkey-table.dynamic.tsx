import dynamic from 'next/dynamic';
import { Skeleton, Text } from '@repo/ui/components';

export const ParentHotkeyTableDynamic = dynamic(
  () => import('./parent-hotkey-table').then((mod) => mod.ParentHotkeyTable),
  {
    ssr: false,
    loading: () => (
      <div className='space-y-4'>
        <div className='space-y-2'>
          <Text level='xsTitle' className='px-3'>
            Hotkeys
          </Text>
          <div className='flex flex-col gap-1'>
            {Array.from({ length: 2 }).map((_, index) => (
              <Skeleton className='h-14 w-full' key={index} />
            ))}
          </div>
        </div>
      </div>
    ),
  }
);
