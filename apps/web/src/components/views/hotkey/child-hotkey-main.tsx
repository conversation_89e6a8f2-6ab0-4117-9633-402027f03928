import type { Hotkey, MetagraphAPI } from '@repo/types/website-api-types';
import { taoDivider } from '@repo/ui/lib';
import { ChildHotkeyTable } from './child-hotkey-table';

export const ChildHotkeyMain = ({
  hotkey,
  data,
  parentHotkey,
}: {
  hotkey: string;
  data?: MetagraphAPI;
  parentHotkey: Hotkey[];
}) => {
  const hotkeyData = parentHotkey.map((item) => {
    return {
      parent: item.parents.map((parent) => ({
        hotkey: parent.hotkey.ss58,
        subnetId: item.netuid,
        stake: Number(parent.proportion_staked) / taoDivider,
        proportion: `${(Number(parent.proportion) * 100).toLocaleString(
          'en-US',
          {
            minimumFractionDigits: 0,
            maximumFractionDigits: 0,
          }
        )}%`,
      })),
      child: {
        hotkey: item.hotkey.ss58,
        stake: Number(item.stake) / taoDivider,
        take: `${(Number(item.childkey_take) * 100).toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 2,
        })}%`,
        subnetId: item.netuid,
      },
    };
  });

  return data?.data
    .filter(
      (item) =>
        hotkeyData.filter((it) => it.child.subnetId === item.netuid).length > 0
    )
    .sort((a, b) => a.netuid - b.netuid)
    .map((item, index) => (
      <ChildHotkeyTable
        data={[item]}
        key={index}
        subnetID={`${item.netuid}`}
        familyData={hotkeyData.find((it) => it.child.subnetId === item.netuid)}
      />
    ));
};
