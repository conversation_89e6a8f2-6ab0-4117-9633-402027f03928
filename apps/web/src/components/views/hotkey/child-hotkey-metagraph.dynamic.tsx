import dynamic from 'next/dynamic';
import { Skeleton } from '@repo/ui/components';

export const ChildHotkeyMetagraphDynamic = dynamic(
  () =>
    import('./child-hotkey-metagraph').then((mod) => mod.ChildHotkeyMetagraph),
  {
    ssr: false,
    loading: () => (
      <div className='flex flex-col gap-1'>
        {Array.from({ length: 2 }).map((_, index) => (
          <Skeleton className='h-14 w-full' key={index} />
        ))}
      </div>
    ),
  }
);
