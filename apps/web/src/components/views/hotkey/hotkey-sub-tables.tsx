"use client";

import { Text } from "@/components/elements/typography";
import cn from "@/lib/cn";
import { useState } from "react";
import { HotkeyMetagraphTable } from "./hotkey-metagraph";
import ExtrinsicTable from "../blockchain/extrinsic-table";
import type { MetagraphAPI } from "@/types";

export function HotkeySubTables({
	hotkey,
	data,
}: {
	hotkey: string;
	data?: MetagraphAPI;
}) {
	const subTabs = ["Metagraph", "Extrinsics"];
	const [currentTab, setCurrentTab] = useState<string>("Metagraph");

	return (
		<div className="flex flex-col gap-9 rounded-2xl border border-neutral-800 bg-neutral-900 p-6 shadow-lg md:p-12">
			<div className="flex gap-0 self-start sm:gap-2">
				{subTabs.map((item, index) => (
					<Text
						key={index}
						className={cn(
							"flex cursor-pointer items-center gap-3 rounded-xl px-3 py-2 text-md hover:bg-neutral-900 sm:px-3 md:text-base md:leading-4",
							currentTab === item
								? "bg-white/10"
								: "border-transparent text-neutral-500"
						)}
						onClick={() => setCurrentTab(item)}
					>
						{item}
					</Text>
				))}
			</div>
			{currentTab === "Metagraph" ? (
				<HotkeyMetagraphTable data={data} hotkey={hotkey} />
			) : (
				<ExtrinsicTable signerAddress={hotkey} />
			)}
		</div>
	);
}
