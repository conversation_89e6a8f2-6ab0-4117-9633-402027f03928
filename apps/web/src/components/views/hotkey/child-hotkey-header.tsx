'use client';

import { useEffect, useMemo, useState } from 'react';
import type { ValidatorLatestQueryParams } from '@repo/types/website-api-types';
import { AddressFormatter, CopyButton, Link, Text } from '@repo/ui/components';
import { appRoutes, taoDivider, useWindowSize } from '@repo/ui/lib';
import { Bittensor } from '@/components/icons/bittensor';
import { useFullValidators } from '@/lib/hooks';
import { walletFormat } from '@/lib/utils';

export function ChildHotkeyHeader({
  data,
  hotkey,
}: {
  data: {
    label: string;
    value: string;
  }[];
  hotkey: string;
}) {
  const { windowSize } = useWindowSize();
  const [validatorParamsAtom, setValidatorParamsAtom] =
    useState<ValidatorLatestQueryParams>({});
  const { data: validatorData } = useFullValidators(validatorParamsAtom);

  const filteredData = useMemo(() => {
    if (!validatorData?.data.length) return data;

    const validator = validatorData.data[0];

    return [
      {
        label: 'Hotkey',
        value: validator.hotkey.ss58,
      },
      {
        label: 'Coldkey',
        value: validator.coldkey.ss58,
      },
      {
        label: 'Validator Stake',
        value: (Number(validator.stake) / taoDivider).toLocaleString(),
      },
      {
        label: 'Validator Take',
        value: `${(Number(validator.take) * 100).toLocaleString('en-US', {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        })}%`,
      },
      {
        label: 'Pending Emission',
        value: (
          Number(validator.pending_emission) / taoDivider
        ).toLocaleString(),
      },
      {
        label: 'Blocks Until Next Reward',
        value: `${validator.blocks_until_next_reward}`,
      },
      {
        label: 'Last Reward Block',
        value: `${validator.last_reward_block}`,
      },
    ];
  }, [validatorData, data]);

  useEffect(() => {
    setValidatorParamsAtom({
      hotkey,
    });
  }, [hotkey, setValidatorParamsAtom]);

  return (
    <div className='flex flex-col gap-2 pt-6'>
      {filteredData.map(({ label, value }) => (
        <div
          className='flex h-max w-full flex-col items-start gap-2 rounded-xl border border-[#CBCBCB1A] bg-[#161616] px-6 py-2 sm:w-fit sm:flex-row sm:items-center'
          key={label}
        >
          <Text level='md' className='w-52 text-[#777777]'>
            {label}
          </Text>
          {label === 'Hotkey' ? (
            <AddressFormatter
              uid={value}
              noIcon
              fullWidth={
                ((windowSize.width ?? 0) > 790 ||
                  (windowSize.width ?? 0) < 640) &&
                (windowSize.width ?? 0) > 520
              }
              className='text-sm'
              rootClassName='flex-1 max-w-none'
              textClassName='whitespace-normal'
            />
          ) : label === 'Coldkey' ? (
            <div className='flex flex-1 items-center gap-2'>
              <Link href={appRoutes.subnets.coldkey(value)}>
                <Text level='md' className='break-all hover:underline'>
                  {((windowSize.width ?? 0) > 840 ||
                    (windowSize.width ?? 0) < 640) &&
                  (windowSize.width ?? 0) > 565
                    ? value
                    : walletFormat(value)}
                </Text>
              </Link>
              <CopyButton value={value} size={14} />
            </div>
          ) : label === 'Last Reward Block' ? (
            <Link href={appRoutes.blockchain.heightDetail(value)}>
              <Text
                level='md'
                className='flex items-center gap-1 break-all hover:underline'
              >
                {value}
              </Text>
            </Link>
          ) : (
            <Text level='md' className='flex items-center break-all'>
              {value}
              {(label === 'Validator Stake' ||
                label === 'Pending Emission') && <Bittensor />}
            </Text>
          )}
        </div>
      ))}
    </div>
  );
}
