import dynamic from 'next/dynamic';
import { Skeleton } from '@repo/ui/components';

export const HotkeyHeaderDynamic = dynamic(
  () => import('./hotkey-header').then((mod) => mod.HotkeyHeader),
  {
    ssr: false,
    loading: () => (
      <div className='flex flex-col gap-2 pt-6'>
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            className='w-max rounded-xl border border-[#CBCBCB1A] bg-[#161616] px-6 py-2'
            key={index}
          >
            <Skeleton className='h-5 w-64' />
          </div>
        ))}
      </div>
    ),
  }
);
