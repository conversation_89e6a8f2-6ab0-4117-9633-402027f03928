import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';

export const PerformanceProportionCell = ({
  row,
}: CellContext<TableData, unknown>) =>
  row.original.proportion !== undefined ? (
    <TableText
      amount={Number(row.original.proportion) * 100}
      className='flex w-14 justify-end !text-white'
      percentage
    />
  ) : (
    <EmptyCell />
  );

export const PerformanceProportionHeader = () => (
  <p className='-mr-2'>Proportion</p>
);
