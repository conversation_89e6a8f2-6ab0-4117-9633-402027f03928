import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';

export const PerformanceTakeCell = ({
  row,
}: CellContext<TableData, unknown>) =>
  row.original.childkey_take !== undefined ? (
    <TableText
      amount={Number(row.original.childkey_take) * 100}
      className={cn(
        'flex w-11 justify-end',
        Number(row.original.childkey_take) > 0
          ? '!text-[#EBC247]'
          : '!text-white'
      )}
      percentage
    />
  ) : (
    <EmptyCell />
  );

export const PerformanceTakeHeader = () => <p className='-mr-2'>CK Take</p>;
