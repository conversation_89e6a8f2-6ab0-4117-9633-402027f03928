import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';

export const PerformanceDominanceCell = ({
  row,
}: CellContext<TableData, unknown>) =>
  row.original.dominance !== undefined ? (
    <TableText
      info={row.original.dominance as string}
      className='w-17 flex justify-end !text-white'
      percentage
    />
  ) : (
    <EmptyCell />
  );

export const PerformanceDominanceHeader = () => (
  <p className='w-17 -mr-2 flex justify-end'>Dominance</p>
);
