import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { DtaoPercentage } from '@/components/views/dtao/dtao-percentage';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';

export const PerformanceFamilyBalanceCell = ({
  row,
}: CellContext<TableData, unknown>) =>
  row.original.family_root_weight !== undefined &&
  row.original.family_alpha !== undefined ? (
    <DtaoPercentage
      root={row.original.family_root_weight as number}
      stake={row.original.family_alpha as number}
      leftContent='Root'
      rightContent='Alpha'
      contentClassName='text-sm'
    />
  ) : (
    <EmptyCell />
  );
