import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';

export const PerformanceSubnetWeightCell = ({
  row,
}: CellContext<TableData, unknown>) =>
  row.original.subnet_weight !== undefined ? (
    <SubnetDtaoWrapper
      info={row.original.subnet_weight as number}
      maximumFractionDigits={0}
      minimumFractionDigits={0}
      currencyClassName='-mr-1'
      className='w-22 flex justify-end'
    />
  ) : (
    <EmptyCell />
  );

export const PerformanceSubnetWeightHeader = () => (
  <p className='w-22 -mr-2 flex justify-end'>Subnet Weight</p>
);
