import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { SubnetDtaoWrapper } from '@/components/views/dtao/subnet-dtao-wrapper';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';

export const PerformanceFamilyWeightCell = ({
  row,
}: CellContext<TableData, unknown>) =>
  row.original.family_subnet_weight !== undefined ? (
    <SubnetDtaoWrapper
      amount={Math.abs(row.original.family_subnet_weight as number)}
      maximumFractionDigits={0}
      minimumFractionDigits={0}
      currencyClassName='-mr-1'
      className='w-22 flex justify-end'
    />
  ) : (
    <EmptyCell />
  );

export const PerformanceFamilyWeightHeader = () => (
  <p className='w-22 -mr-2 flex justify-end'>Family Weight</p>
);
