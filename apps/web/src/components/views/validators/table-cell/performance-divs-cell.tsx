import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';
import { cn } from '@repo/ui/lib';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';
import { useValidatorDominance } from '@/lib/hooks/use-validator-dominance';

export const PerformanceDivsCell = ({
  row,
}: CellContext<TableData, unknown>) => {
  const { dominance } = useValidatorDominance();

  return row.original.dividends !== undefined ? (
    <TableText
      info={row.original.dividends as string}
      className={cn(
        'flex w-16 justify-end',
        Number(row.original.dividends) > dominance / 100
          ? '!text-white'
          : '!text-rose-500'
      )}
      u16Percentage
    />
  ) : (
    <EmptyCell />
  );
};

export const PerformanceDivsHeader = () => (
  <p className='-mr-2 flex w-16 justify-end'>Divs</p>
);
