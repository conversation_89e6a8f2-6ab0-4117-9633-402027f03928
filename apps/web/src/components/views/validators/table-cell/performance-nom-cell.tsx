import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { TableText } from '@repo/ui/components';
import { EmptyCell } from '@/components/views/validators/validator-performance-table-empty-cell';

export const PerformanceNomCell = ({ row }: CellContext<TableData, unknown>) =>
  row.original.nominators !== undefined ? (
    <TableText
      info={row.original.nominators as string}
      className='flex w-10 justify-end !text-white'
    />
  ) : (
    <EmptyCell />
  );

export const PerformanceNomHeader = () => (
  <p className='-mr-2 flex w-11 justify-end'>Noms</p>
);
