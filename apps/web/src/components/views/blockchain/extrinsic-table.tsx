'use client';

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import type { SortingState } from '@tanstack/react-table';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import type {
  ColumnSchema,
  ExtrinsicParamsAtom,
} from '@repo/types/website-api-types';
import { ExtrinsicOrder, NetworkType } from '@repo/types/website-api-types';
import { BubbleTable, Switch, Text } from '@repo/ui/components';
import {
  ExtrinsicAccountCell,
  ExtrinsicHeightCell,
  ExtrinsicIdCell,
  ExtrinsicNameCell,
  ExtrinsicResultCell,
  ExtrinsicTimeCell,
  ExtrinsicTimeHeader,
} from './table-cell';
import TablePagination from './table-pagination';
import { TransferNetworkSelector } from '@/components/elements/transfer-network-selector';
import { REFETCH_INTERVAL } from '@/lib/config';
import { useExtrinsic } from '@/lib/hooks';
import { mapOrderToEnum } from '@/lib/utils';

export default function ExtrinsicTable({
  signerAddress,
  blockNumber,
  hash,
  network,
  noNetworkFilter,
  stickyHeaderClass,
}: {
  signerAddress?: string;
  blockNumber?: number;
  hash?: string;
  network?: NetworkType;
  noNetworkFilter?: boolean;
  stickyHeaderClass?: string;
}) {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();

  const [total, setTotal] = useState<number>(0);

  const [extrinsicParams, setExtrinsicParams] = useState<ExtrinsicParamsAtom>(
    {}
  );

  const columns = useMemo<ColumnSchema[]>(
    () => [
      {
        id: 'id',
        header: 'Extrinsic ID',
        cell: ExtrinsicIdCell,
      },
      {
        id: 'full_name',
        header: 'Name',
        cell: ExtrinsicNameCell,
        enableSorting: false,
      },
      {
        id: 'signer_address',
        header: 'Account',
        cell: ExtrinsicAccountCell,
      },
      {
        id: 'block_number',
        header: 'Height',
        cell: ExtrinsicHeightCell,
      },
      {
        id: 'success',
        header: 'Result',
        cell: ExtrinsicResultCell,
      },
      {
        id: 'timestamp',
        header: ExtrinsicTimeHeader,
        cell: ExtrinsicTimeCell,
      },
    ],
    []
  );

  const { data: extrinsicData, isPending } = useExtrinsic(extrinsicParams);

  const tableData = useMemo(() => {
    return (
      extrinsicData?.data.map((item) => ({
        id: item.id,
        signer_address: item.signer_address,
        timestamp: item.timestamp,
        success: item.success,
        full_name: item.full_name,
        block_number: item.block_number,
        network: !extrinsicParams.network
          ? network
            ? network
            : NetworkType.Finney
          : (extrinsicParams.network as NetworkType),
      })) ?? []
    );
  }, [extrinsicData, extrinsicParams.network, network]);

  const handleFilterChange = useCallback(
    (filter: string, sorting: SortingState) => {
      const params = new URLSearchParams(searchParams);

      let isChanged = false;

      if (sorting.length > 0) {
        params.set(
          'order',
          `${sorting[0].id}:${sorting[0].desc ? 'desc' : 'asc'}`
        );
        isChanged = true;
      } else if (params.get('order')) {
        params.delete('order');
        isChanged = true;
      }

      const currentString = params.get('full_name') ?? '';
      if (currentString !== filter) {
        if (filter.length > 0) {
          params.set('full_name', filter);
          params.delete('page');
          isChanged = true;
        } else {
          params.delete('full_name');
          params.delete('page');
          isChanged = true;
        }
      }

      if (isChanged) {
        router.push(`${pathname}?${params.toString()}`, { scroll: false });
      }
    },
    [router, pathname, searchParams]
  );

  useEffect(() => {
    const page = searchParams.get('page');
    const limit = searchParams.get('limit');
    // const fullName = searchParams.get("full_name");
    const order = searchParams.get('order');
    const networkFilter = searchParams.get('network');

    setExtrinsicParams((prev) => ({
      page: page ? Number(page) : undefined,
      limit: Number(limit ?? '10'),
      // fullName: fullName ?? undefined,
      order: order ? mapOrderToEnum(order, ExtrinsicOrder) : undefined,
      signerAddress,
      blockNumber,
      hash,
      network:
        !networkFilter || (networkFilter as NetworkType) === NetworkType.Finney
          ? undefined
          : (networkFilter as NetworkType),
      refetchInterval: prev.refetchInterval,
    }));
  }, [searchParams, setExtrinsicParams, signerAddress, blockNumber, hash]);

  useEffect(() => {
    if (extrinsicData) {
      setTotal(extrinsicData.pagination.total_items);
    }
  }, [extrinsicData]);

  return (
    <div className='flex flex-col pt-5'>
      <div className='flex items-center gap-1 self-end'>
        <Switch
          className='h-4 w-7'
          thumbClassName='h-3 w-3 data-[state=checked]:translate-x-3'
          checked={REFETCH_INTERVAL === extrinsicParams.refetchInterval}
          onCheckedChange={(e) => {
            e
              ? setExtrinsicParams((prev) => ({
                  ...prev,
                  refetchInterval: REFETCH_INTERVAL,
                }))
              : setExtrinsicParams((prev) => ({
                  ...prev,
                  refetchInterval: REFETCH_INTERVAL * 100,
                }));
          }}
        />
        <Text level='sm' className='opacity-40'>
          Auto Refresh
        </Text>
      </div>
      <div className='flex flex-col gap-3'>
        <BubbleTable
          // stickyHeader
          // stickyHeaderClass={stickyHeaderClass}
          columnSchemas={columns}
          rowData={tableData}
          // searchable
          selector
          isFetching={isPending}
          searchPlaceholder='Search by Name'
          initialPageSize={10}
          onFilterChange={handleFilterChange}
          amountFilter={
            noNetworkFilter ? undefined : (
              <TransferNetworkSelector
                defaultValue={network ?? 'finney'}
                hasAll={false}
              />
            )
          }
        />
        <TablePagination total={total} />
      </div>
    </div>
  );
}
