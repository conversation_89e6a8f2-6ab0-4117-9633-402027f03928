import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { StaticTableDateFormatter } from '@repo/ui/components';

export const ExtrinsicTimeCell = ({ row }: CellContext<TableData, unknown>) => (
  <StaticTableDateFormatter timestamp={row.original.timestamp as string} />
);

export const ExtrinsicTimeHeader = () => <span className='-mr-2'>Time</span>;
