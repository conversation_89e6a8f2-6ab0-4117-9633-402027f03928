import type { CellContext } from '@tanstack/react-table';
import type { TableData } from '@repo/types/website-api-types';
import { Link, TableText } from '@repo/ui/components';
import { appRoutes } from '@repo/ui/lib';

export const ExtrinsicHeightCell = ({
  row,
}: CellContext<TableData, unknown>) => (
  <Link
    href={appRoutes.blockchain.heightDetail(
      row.original.block_number as string,
      row.original.network as string
    )}
    className='flex gap-2'
  >
    <TableText
      className='hover:underline'
      info={row.original.block_number as string}
    />
  </Link>
);
