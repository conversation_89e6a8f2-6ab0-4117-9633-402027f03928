import { Suspense } from 'react';
import { SkeletonTable } from '@repo/ui/components';
import { fetchDtaoSubnet } from '@/api-proxy/dtao';
import { ValidatorTableGroup } from '@/components/views/validators/validator-table-group';

// export async function generateMetadata({
// 	params: { path },
// }: {
// 	params: { path: string };
// }): Promise<Metadata | null | undefined> {
// 	const validatorName = await fetchIdentityId(path);

// 	const truncatedValidatorName =
// 		validatorName.length > 8
// 			? `${validatorName.slice(0, 8)}...`
// 			: validatorName;

// 	return constructMetadata({
// 		title: `${truncatedValidatorName} · Validator · taostats`,
// 	});
// }

export default function ValidatorDetailsPage({
  params,
}: {
  params: { path: string };
}) {
  return (
    <Suspense fallback={<SkeletonTable length={10} />}>
      asdfasdfasdfawdfasdfasdf
      <ValidatorDetails params={params} />
    </Suspense>
  );
}

async function ValidatorDetails({
  params: { path },
}: {
  params: { path: string };
}) {
  const responses = await Promise.allSettled([fetchDtaoSubnet({})]);

  const subnets =
    responses[0].status === 'fulfilled' ? responses[0].value : null;

  return (
    <div className='min-h-[30rem] px-2 py-8 md:px-12'>
      asdfasdf-----------------
      <ValidatorTableGroup address={path} subnets={subnets} />
    </div>
  );
}
