import { notFound } from 'next/navigation';
import { taoDivider } from '@repo/ui/lib';
import { fetchHotkey } from '@/api-proxy/hotkey';
import { fetchMetagraph } from '@/api-proxy/metagraph';
import { fetchFullValidators } from '@/api-proxy/validators';
import { ChildHotkeyMain } from '@/components/views/hotkey/child-hotkey-main';
import { HotkeyHeaderDynamic } from '@/components/views/hotkey/hotkey-header.dynamic';
import { HotkeyMainSection } from '@/components/views/hotkey/hotkey-main';

export default async function HotkeyPage({
  params: { address },
}: {
  params: { address: string };
}) {
  const responses = await Promise.allSettled([
    fetchMetagraph({ hotkey: address }),
    fetchHotkey({ hotkey: address, limit: 100 }),
  ]);

  const hotkeyData =
    responses[0].status === 'fulfilled' ? responses[0].value : undefined;

  const hotkeyFamilyData =
    responses[1].status === 'fulfilled' ? responses[1].value : null;

  const hotkeyDetails = hotkeyData?.data[0];
  if (!hotkeyDetails) {
    notFound();
  }

  const parentHotkeyData = hotkeyFamilyData?.data.filter(
    (item) => item.parents.length > 0
  );

  const headerInfo = [
    {
      label: 'Hotkey',
      value: address,
    },
    {
      label: 'Coldkey',
      value: hotkeyDetails.coldkey.ss58,
    },
  ];

  if (parentHotkeyData && parentHotkeyData.length > 0) {
    const validatorResponse = await Promise.allSettled([
      fetchFullValidators({ hotkey: address }),
    ]);

    const validatorData =
      validatorResponse[0].status === 'fulfilled'
        ? validatorResponse[0].value
        : null;
    const currentValidator = validatorData?.data[0];

    if (currentValidator) {
      headerInfo.push(
        ...[
          {
            label: 'Validator Stake',
            value: (
              Number(currentValidator.stake) / taoDivider
            ).toLocaleString(),
          },
          {
            label: 'Validator Take',
            value: `${(Number(currentValidator.take) * 100).toLocaleString(
              'en-US',
              {
                minimumFractionDigits: 0,
                maximumFractionDigits: 0,
              }
            )}%`,
          },
          {
            label: 'Pending Emission',
            value: (
              Number(currentValidator.pending_emission) / taoDivider
            ).toLocaleString(),
          },
          {
            label: 'Blocks Until Next Reward',
            value: `${currentValidator.blocks_until_next_reward}`,
          },
          {
            label: 'Last Reward Block',
            value: `${currentValidator.last_reward_block}`,
          },
        ]
      );
    }
  }

  return (
    <div className='flex flex-col gap-6 px-3 pb-14 sm:px-10'>
      <HotkeyHeaderDynamic
        data={headerInfo}
        hotkey={address}
        isChildHotkey={
          parentHotkeyData ? parentHotkeyData.length > 0 : undefined
        }
      />
      {parentHotkeyData && parentHotkeyData.length > 0 ? (
        <ChildHotkeyMain
          hotkey={address}
          data={hotkeyData}
          parentHotkey={parentHotkeyData}
        />
      ) : (
        <HotkeyMainSection hotkey={address} data={hotkeyData} />
      )}
    </div>
  );
}
