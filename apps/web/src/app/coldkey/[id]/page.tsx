import { notFound } from 'next/navigation';
import { fetchMetagraph } from '@/api-proxy/metagraph';
import { fetchFullValidators } from '@/api-proxy/validators';
import ColdkeyWrapper from '@/components/views/coldkey/coldkey-wrapper';

export default async function ColdKeyDetailPage({
  params: { id },
}: {
  params: { id: string };
}) {
  const coldkeyResponse = await Promise.allSettled([
    fetchMetagraph({ coldkey: id }),
  ]);

  const coldkeyData =
    coldkeyResponse[0].status === 'fulfilled' ? coldkeyResponse[0].value : null;

  if (!coldkeyData?.data.length || coldkeyData.data.length === 0)
    return notFound();

  let totalStake = 0;
  let dailyReward = 0;

  if (
    coldkeyData.data &&
    Number(coldkeyData.data[0].validator_trust) > 0 &&
    coldkeyData.data[0].is_child_key
  ) {
    const validatorResponse = await Promise.allSettled([
      fetchFullValidators({ hotkey: coldkeyData.data[0].hotkey.ss58 }),
    ]);

    const validator =
      validatorResponse[0].status === 'fulfilled'
        ? validatorResponse[0].value
        : null;

    if (validator && validator.data.length > 0) {
      totalStake = Number(validator.data[0].stake);
      dailyReward = Number(validator.data[0].total_daily_return);
    }
  } else {
    const hotkeys: string[] = [];

    coldkeyData.data.forEach(({ hotkey, daily_reward: dailyValue, stake }) => {
      dailyReward += Number(dailyValue);
      if (!hotkeys.includes(hotkey.ss58)) hotkeys.push(hotkey.ss58);
      else return;

      totalStake += Number(stake);
    });
  }

  return (
    <ColdkeyWrapper
      id={id}
      initialData={coldkeyData}
      totalStake={totalStake}
      dailyReward={dailyReward}
    />
  );
}
