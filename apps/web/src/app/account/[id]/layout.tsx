import type { Metadata } from 'next';
import AccountTabs from '@/components/views/account/account-tabs';
import { DtaoWalletDynamic } from '@/components/views/account/wallet.dynamic';
import { constructMetadata } from '@/lib/meta';
import { encodeSS58Address } from '@/lib/utils/encoding';

export function generateMetadata({
  params: { id },
}: {
  params: { id: string };
}): Metadata {
  return constructMetadata({
    title: `${id.slice(0, 8)}... · Account · taostats`,
  });
}

export default function WalletLayout({
  children,
  params: { id },
}: {
  params: { id: string };
  children: React.ReactNode;
}) {
  const address = encodeSS58Address(id);

  return (
    <div className='relative mt-12 flex flex-col gap-10'>
      <DtaoWalletDynamic params={{ id: address }} />
      <div className='pb-18 flex flex-col gap-3 p-4 sm:px-6 md:px-8'>
        <AccountTabs path={address} />
        {children}
      </div>
    </div>
  );
}
