import { PoolOrder } from '@repo/types/website-api-types';
import { fetchAllStakeBalances, fetchDtaoSubnet } from '@/api-proxy/dtao';
import { BalancesDynamic } from '@/components/views/account/balances.dynamic';

export default async function DelegationPage({
  params: { id },
}: {
  params: { id: string };
}) {
  const walletData = await Promise.allSettled([
    fetchAllStakeBalances({ coldkey: id }),
    fetchDtaoSubnet({ order: PoolOrder.MarketCapDesc, limit: 200 }),
  ]);

  const stakeBalance =
    walletData[0].status === 'fulfilled' ? walletData[0].value : null;

  const subnets =
    walletData[1].status === 'fulfilled' ? walletData[1].value : null;

  return (
    <BalancesDynamic
      params={{ id }}
      stakeBalanceData={stakeBalance}
      subnets={subnets}
    />
  );
}
